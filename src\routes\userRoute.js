import express from 'express';
const router = express.Router();
import { successResponse } from '../utils/apiResponseUltil.js';
import { getUserAnalyticsController } from '../controllers/userController.js';

// Ví dụ một route được bảo vệ
// Middleware authMiddleware sẽ được áp dụng trước khi request đến route này (trong src/index.js)
router.get('/protected-data', (req, res) => {
  // Middleware đã xác thực token và gắn thông tin user vào req.user
  // Bạn có thể sử dụng req.user ở đây nếu cần
  res.json(
    successResponse(
      'Đây là dữ liệu được bảo vệ!',
      { user: req.user } // Thông tin user từ token
    )
  );
});

router.get('/analytic', getUserAnalyticsController);
export default router;
