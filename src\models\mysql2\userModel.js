import BaseModel from '../shared/BaseModel.js';
import { Enums } from '../../config/constants.js';
import { Fields, Utils } from '../shared/helpers.js';
import MySQL2Service from '../../services/mysql2Service.js';

export default class UserModel extends BaseModel {
  static get tableName() {
    return 'user';
  }

  static getFields() {
    return Object.assign({}, super.getBaseFields(), {
      user_id: Fields.userId(),
      user_name: Fields.userName(),
      email: Fields.email(),
      phone: Fields.phone(),
      status: Fields.status(),
      ip: Fields.ip(),
      create_time: Fields.createTime(),
    });
  }

  static getOptions() {
    return Object.assign({}, super.getOptions(), {
      indexes: [
        { fields: ['user_id'] },
        { fields: ['user_name'], unique: true },
        { fields: ['email'] },
        { fields: ['status'] },
        { fields: ['ip'] },
        { fields: ['create_time'] },
        { fields: ['user_id', 'status'] },
        { fields: ['create_time', 'status'] },
      ],
    });
  }

  static getStatusEnum() {
    return Enums.USER_STATUS;
  }

  static transform(row) {
    if (!row) return null;

    return {
      ...row,
      ip: Utils.formatIP(row.ip),
      email: Utils.sanitizeString(row.email),
      phone: Utils.sanitizeString(row.phone),
      createTimeFormatted: row.create_time ? Utils.timestampToDate(row.create_time) : null,
    };
  }

  static validate(data) {
    super.validate(data);

    if (data.email && !Utils.isValidEmail(data.email)) {
      throw new Error('Invalid email format');
    }

    if (data.phone && !Utils.isValidPhone(data.phone)) {
      throw new Error('Invalid phone format');
    }

    return true;
  }

  static async findActive(options = {}) {
    return await MySQL2Service.findActive(this.tableName, options, this.transform);
  }

  static async findByIP(ip, options = {}) {
    return await MySQL2Service.findByIP(this.tableName, ip, options, this.transform);
  }

  static async findToday(options = {}) {
    return await MySQL2Service.findToday(this.tableName, options, this.transform);
  }

  static async findThisMonth(options = {}) {
    return await MySQL2Service.findThisMonth(this.tableName, options, this.transform);
  }

  static async findByTimeRange(startTime, endTime, options = {}) {
    return await MySQL2Service.findByTimeRange(
      this.tableName,
      startTime,
      endTime,
      options,
      this.transform
    );
  }

  static async findByUserId(userId) {
    return await MySQL2Service.findById(this.tableName, 'user_id', userId, this.transform);
  }

  static async countByStatus(status) {
    return await MySQL2Service.countDocuments(this.tableName, { status });
  }

  static async estimatedDocumentCount() {
    return await MySQL2Service.estimatedDocumentCount(this.tableName);
  }

  static async countDocuments(conditions = {}) {
    return await MySQL2Service.countDocuments(this.tableName, conditions);
  }

  static async distinct(field, conditions = {}) {
    return await MySQL2Service.distinct(this.tableName, field, conditions);
  }
}
