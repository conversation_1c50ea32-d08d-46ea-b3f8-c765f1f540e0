import { mysqlPool } from '../config/mysql.js';
import logger from '../config/logger.js';

class MySQLService {
  // Thực thi query đơn giản với nhiều tùy chọn
  static async query(sql, params = [], options = {}) {
    try {
      const { debug = false } = options;

      if (debug) {
        logger.debug(`🔍 MySQL Query: ${sql}`, { params });
      }

      const [rows] = await mysqlPool.execute(sql, params);

      if (debug) {
        logger.debug(`✅ MySQL Query Result: ${rows.length} rows`);
      }

      return rows;
    } catch (error) {
      logger.error('❌ MySQL Query Error:', {
        sql,
        params,
        error: error.message,
        code: error.code,
      });
      throw error;
    }
  }

  // Lấy một bản ghi với validation
  static async getOne(sql, params = [], options = {}) {
    try {
      const rows = await this.query(sql, params, options);
      const result = rows[0] || null;

      if (options.required && !result) {
        throw new Error(`Record not found for query: ${sql}`);
      }

      return result;
    } catch (error) {
      logger.error('❌ MySQL GetOne Error:', error);
      throw error;
    }
  }

  // Lấy nhiều bản ghi với tùy chọn
  static async getAll(sql, params = [], options = {}) {
    try {
      const { limit, offset, orderBy } = options;
      let finalSql = sql;

      if (orderBy) {
        finalSql += ` ORDER BY ${orderBy}`;
      }

      if (limit) {
        finalSql += ` LIMIT ${limit}`;
        if (offset) {
          finalSql += ` OFFSET ${offset}`;
        }
      }

      return await this.query(finalSql, params, options);
    } catch (error) {
      logger.error('❌ MySQL GetAll Error:', error);
      throw error;
    }
  }

  // Thêm bản ghi mới với validation và tùy chọn
  static async insert(table, data, options = {}) {
    try {
      const { ignore = false, onDuplicateUpdate = null, returnFields = '*' } = options;

      // Validation
      if (!table || !data || Object.keys(data).length === 0) {
        throw new Error('Table name and data are required');
      }

      const columns = Object.keys(data);
      const values = Object.values(data);
      const placeholders = columns.map(() => '?').join(', ');

      let sql = `INSERT ${ignore ? 'IGNORE ' : ''}INTO ${table} (${columns.join(', ')}) VALUES (${placeholders})`;

      if (onDuplicateUpdate) {
        const updateColumns = Object.keys(onDuplicateUpdate)
          .map((key) => `${key} = ?`)
          .join(', ');
        sql += ` ON DUPLICATE KEY UPDATE ${updateColumns}`;
        values.push(...Object.values(onDuplicateUpdate));
      }

      if (returnFields !== '*') {
        sql += ` RETURNING ${returnFields}`;
      }

      const result = await this.query(sql, values, options);

      return {
        insertId: result.insertId,
        affectedRows: result.affectedRows,
        data: returnFields !== '*' ? result : null,
      };
    } catch (error) {
      logger.error('❌ MySQL Insert Error:', error);
      throw error;
    }
  }

  // Cập nhật bản ghi với validation nâng cao
  static async update(table, data, where, whereParams = [], options = {}) {
    try {
      const { limit, orderBy, returnFields = null } = options;

      // Validation
      if (!table || !data || Object.keys(data).length === 0) {
        throw new Error('Table name and update data are required');
      }

      const setColumns = Object.keys(data)
        .map((key) => `${key} = ?`)
        .join(', ');
      const setValues = Object.values(data);

      let sql = `UPDATE ${table} SET ${setColumns} WHERE ${where}`;

      if (orderBy) {
        sql += ` ORDER BY ${orderBy}`;
      }

      if (limit) {
        sql += ` LIMIT ${limit}`;
      }

      if (returnFields) {
        sql += ` RETURNING ${returnFields}`;
      }

      const result = await this.query(sql, [...setValues, ...whereParams], options);

      return {
        affectedRows: result.affectedRows,
        data: returnFields ? result : null,
      };
    } catch (error) {
      logger.error('❌ MySQL Update Error:', error);
      throw error;
    }
  }

  // Xóa bản ghi với tùy chọn
  static async delete(table, where, params = [], options = {}) {
    try {
      const { limit, orderBy, returnFields = null } = options;

      if (!table || !where) {
        throw new Error('Table name and where condition are required');
      }

      let sql = `DELETE FROM ${table} WHERE ${where}`;

      if (orderBy) {
        sql += ` ORDER BY ${orderBy}`;
      }

      if (limit) {
        sql += ` LIMIT ${limit}`;
      }

      if (returnFields) {
        sql += ` RETURNING ${returnFields}`;
      }

      const result = await this.query(sql, params, options);

      return {
        affectedRows: result.affectedRows,
        data: returnFields ? result : null,
      };
    } catch (error) {
      logger.error('❌ MySQL Delete Error:', error);
      throw error;
    }
  }

  // Đếm số bản ghi với tùy chọn
  static async count(table, where = '1', params = [], options = {}) {
    try {
      const { distinct = null, alias = 'total' } = options;

      let sql = `SELECT COUNT(${distinct ? `DISTINCT ${distinct}` : '*'}) as ${alias} FROM ${table}`;

      if (where && where !== '1') {
        sql += ` WHERE ${where}`;
      }

      const result = await this.getOne(sql, params, options);
      if (!result) return 0;

      // Safe access to result properties
      let value = 0;
      if (alias === 'total' && result.total !== undefined) {
        value = result.total;
      } else if (alias === 'count' && result.count !== undefined) {
        value = result.count;
      } else if (alias === 'user_count' && result.user_count !== undefined) {
        value = result.user_count;
      } else if (alias === 'unique_count' && result.unique_count !== undefined) {
        value = result.unique_count;
      }

      return Number(value) || 0;
    } catch (error) {
      logger.error('❌ MySQL Count Error:', error);
      throw error;
    }
  }

  // Phân trang nâng cao với nhiều tùy chọn
  static async paginate(
    table,
    page = 1,
    limit = 10,
    where = '1',
    whereParams = [],
    orderBy = 'id DESC',
    options = {}
  ) {
    try {
      const { select = '*', joins = '', groupBy = '', having = '', debug = false } = options;

      const offset = (page - 1) * limit;

      // Build base query
      let baseQuery = `SELECT ${select} FROM ${table}`;
      if (joins) baseQuery += ` ${joins}`;
      if (where && where !== '1') baseQuery += ` WHERE ${where}`;
      if (groupBy) baseQuery += ` GROUP BY ${groupBy}`;
      if (having) baseQuery += ` HAVING ${having}`;

      // Get data
      const dataSql = `${baseQuery} ORDER BY ${orderBy} LIMIT ? OFFSET ?`;
      const data = await this.query(dataSql, [...whereParams, limit, offset], { debug });

      // Get total count
      const countSql = `SELECT COUNT(*) as total FROM (${baseQuery}) as count_table`;
      const total = await this.count('', '1', whereParams, {
        customQuery: countSql,
        debug,
      });

      const totalPages = Math.ceil(total / limit);

      return {
        data,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1,
          startIndex: offset + 1,
          endIndex: Math.min(offset + limit, total),
        },
        meta: {
          query: debug ? dataSql : null,
          executionTime: Date.now(),
        },
      };
    } catch (error) {
      logger.error('❌ MySQL Paginate Error:', error);
      throw error;
    }
  }

  // Transaction với retry logic
  static async transaction(callback, options = {}) {
    const { maxRetries = 3, retryDelay = 1000 } = options;
    let lastError;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      const connection = await mysqlPool.getConnection();

      try {
        await connection.beginTransaction();
        const result = await callback(connection);
        await connection.commit();
        connection.release();
        return result;
      } catch (error) {
        await connection.rollback();
        connection.release();
        lastError = error;

        if (attempt < maxRetries) {
          logger.warn(`⚠️ Transaction attempt ${attempt} failed, retrying...`, error.message);
          await new Promise((resolve) => setTimeout(resolve, retryDelay * attempt));
        }
      }
    }

    logger.error('❌ MySQL Transaction Error after all retries:', lastError);
    throw lastError;
  }

  // Batch operations
  static async batchInsert(table, dataArray, options = {}) {
    try {
      const { batchSize = 1000, ignore = false } = options;
      const results = [];

      for (let i = 0; i < dataArray.length; i += batchSize) {
        const batch = dataArray.slice(i, i + batchSize);
        const result = await this.insertMany(table, batch, { ignore });
        results.push(result);
      }

      return results;
    } catch (error) {
      logger.error('❌ MySQL Batch Insert Error:', error);
      throw error;
    }
  }

  // Insert nhiều bản ghi cùng lúc
  static async insertMany(table, dataArray, options = {}) {
    try {
      const { ignore = false, onDuplicateUpdate = null } = options;

      if (!dataArray || dataArray.length === 0) {
        throw new Error('Data array is required');
      }

      const columns = Object.keys(dataArray[0]);
      const placeholders = dataArray.map(() => `(${columns.map(() => '?').join(', ')})`).join(', ');
      const values = dataArray.flatMap((row) => Object.values(row));

      let sql = `INSERT ${ignore ? 'IGNORE ' : ''}INTO ${table} (${columns.join(', ')}) VALUES ${placeholders}`;

      if (onDuplicateUpdate) {
        const updateColumns = Object.keys(onDuplicateUpdate)
          .map((key) => `${key} = ?`)
          .join(', ');
        sql += ` ON DUPLICATE KEY UPDATE ${updateColumns}`;
        values.push(...Object.values(onDuplicateUpdate));
      }

      const result = await this.query(sql, values, options);
      return result;
    } catch (error) {
      logger.error('❌ MySQL InsertMany Error:', error);
      throw error;
    }
  }

  // Raw query với connection riêng
  static async rawQuery(sql, params = []) {
    const connection = await mysqlPool.getConnection();
    try {
      const [rows] = await connection.execute(sql, params);
      return rows;
    } catch (error) {
      logger.error('❌ MySQL Raw Query Error:', error);
      throw error;
    } finally {
      connection.release();
    }
  }

  // Kiểm tra kết nối
  static async testConnection() {
    try {
      const connection = await mysqlPool.getConnection();
      connection.release();
      return true;
    } catch (error) {
      logger.error('❌ MySQL Connection Test Failed:', error);
      return false;
    }
  }

  // Lấy thông tin connection pool
  static getPoolInfo() {
    return {
      threadId: mysqlPool.threadId,
      config: mysqlPool.config || {},
      pool: mysqlPool.pool || {},
    };
  }

  // Escape string để tránh SQL injection
  static escape(value) {
    if (typeof value === 'string') {
      return value.replace(/'/g, "''");
    }
    return value;
  }

  // Build WHERE clause từ object
  static buildWhereClause(conditions, operator = 'AND') {
    if (!conditions || Object.keys(conditions).length === 0) {
      return { clause: '1', params: [] };
    }

    const clauses = [];
    const params = [];

    for (const [key, value] of Object.entries(conditions)) {
      if (value !== null && value !== undefined) {
        clauses.push(`${key} = ?`);
        params.push(value);
      }
    }

    return {
      clause: clauses.length > 0 ? clauses.join(` ${operator} `) : '1',
      params,
    };
  }

  // Build ORDER BY clause
  static buildOrderByClause(sortBy, sortOrder = 'ASC') {
    if (!sortBy) return '';

    const validOrders = ['ASC', 'DESC'];
    const order = validOrders.includes(sortOrder.toUpperCase()) ? sortOrder.toUpperCase() : 'ASC';

    return `${sortBy} ${order}`;
  }
}

export default MySQLService;
