import { UserModel, SignUpSourceModel } from '../models/shared/index.js';
import MySQL2Service from './mysql2Service.js';
import logger from '../config/logger.js';
import AppError from './errorService.js';
import HTTPStatus from 'http-status';
import { getStartOfDay, getEndOfDay } from '../utils/timeUtil.js';

/**
 * Generate user analytics with aggregation by different dimensions
 * @param {Object} filters - Query filters including date range
 * @returns {Object} Analytics data
 */
export const getUserAnalytics = async (filters = {}) => {
  try {
    // Build date filter if startDate/endDate provided
    const dateConditions = {};
    if (filters.startDate && filters.endDate) {
      dateConditions.createTime = {
        $gte: getStartOfDay(filters.startDate),
        $lte: getEndOfDay(filters.endDate),
      };
    } else if (filters.startDate) {
      dateConditions.createTime = {
        $gte: getStartOfDay(filters.startDate),
      };
    } else if (filters.endDate) {
      dateConditions.createTime = {
        $lte: getEndOfDay(filters.endDate),
      };
    }

    // Users by date (using raw SQL for aggregation)
    const usersByDate = await getUsersByDate(dateConditions);

    // Users by IP (using raw SQL for aggregation)
    const usersByIp = await getUsersByIp(dateConditions);

    // Users by UTM source
    const usersByUtmSource = await getUsersByUtmSource(dateConditions);

    // Users by UTM content
    const usersByUtmContent = await getUsersByUtmContent(dateConditions);

    return {
      usersByDate,
      usersByIp,
      usersByUtmSource,
      usersByUtmContent,
    };
  } catch (error) {
    // Handle MySQL2 timeout specifically
    if (error.code === 'ETIMEDOUT' || error.code === 'ECONNRESET') {
      logger.error('MySQL2 operation timed out:', error);
      throw new AppError(
        'Database query timed out. Please try with a narrower date range or contact support.',
        HTTPStatus.REQUEST_TIMEOUT,
        true
      );
    }

    logger.error('Error generating user analytics:', error);
    throw error;
  }
};

/**
 * Get users grouped by date
 * @param {Object} dateConditions - Date filter conditions
 */
async function getUsersByDate(dateConditions) {
  try {
    const { whereClause, params } = MySQL2Service.buildWhereClause(dateConditions);

    let sql = `
      SELECT 
        DATE(FROM_UNIXTIME(create_time)) as date,
        COUNT(*) as count
      FROM ${UserModel.tableName}
    `;

    if (whereClause !== '1') {
      sql += ` WHERE ${whereClause}`;
    }

    sql += ` GROUP BY DATE(FROM_UNIXTIME(create_time)) ORDER BY date`;

    const rows = await MySQL2Service.query(sql, params);
    return rows.map((row) => ({
      _id: row.date,
      count: Number(row.count),
    }));
  } catch (error) {
    logger.error('Error getting users by date:', error);
    throw error;
  }
}

/**
 * Get users grouped by IP with date breakdown
 * @param {Object} dateConditions - Date filter conditions
 */
async function getUsersByIp(dateConditions) {
  try {
    const { whereClause, params } = MySQL2Service.buildWhereClause(dateConditions);

    let sql = `
      SELECT 
        DATE(FROM_UNIXTIME(create_time)) as date,
        ip,
        COUNT(*) as count
      FROM ${UserModel.tableName}
    `;

    // Always need WHERE clause for IP conditions
    if (whereClause !== '1') {
      sql += ` WHERE ${whereClause} AND ip IS NOT NULL AND ip != ''`;
    } else {
      sql += ` WHERE ip IS NOT NULL AND ip != ''`;
    }

    sql += ` 
      GROUP BY DATE(FROM_UNIXTIME(create_time)), ip
      ORDER BY date, count DESC
    `;

    const rows = await MySQL2Service.query(sql, params);

    // Group by date using Map to avoid object injection
    const groupedByDate = new Map();
    rows.forEach((row) => {
      const date = row?.date;
      const ip = row?.ip;
      const count = row?.count;

      if (!date) return; // Skip if no date

      const dateKey = String(date); // Ensure safe key
      if (!groupedByDate.has(dateKey)) {
        groupedByDate.set(dateKey, {
          _id: date,
          ips: [],
          totalIpCount: 0,
        });
      }
      const ipData = {
        _id: ip,
        count: Number(count),
      };
      const dateGroup = groupedByDate.get(dateKey);
      if (dateGroup) {
        dateGroup.ips.push(ipData);
        dateGroup.totalIpCount++;
      }
    });

    return Array.from(groupedByDate.values()).sort((a, b) => a._id.localeCompare(b._id));
  } catch (error) {
    logger.error('Error getting users by IP:', error);
    throw error;
  }
}

/**
 * Get users grouped by UTM source
 * @param {Object} dateConditions - Date filter conditions
 */
async function getUsersByUtmSource(dateConditions) {
  try {
    const { whereClause, params } = MySQL2Service.buildWhereClause(dateConditions);

    let sql = `
      SELECT 
        s.utm_source as _id,
        COUNT(*) as count
      FROM ${SignUpSourceModel.tableName} s
      INNER JOIN ${UserModel.tableName} u ON s.user_id = u.user_id
    `;

    if (whereClause !== '1') {
      // Replace field names with table alias for user table
      const userWhereClause = whereClause.replace(/create_time/g, 'u.create_time');
      sql += ` WHERE ${userWhereClause} AND s.utm_source IS NOT NULL AND s.utm_source != ''`;
    } else {
      sql += ` WHERE s.utm_source IS NOT NULL AND s.utm_source != ''`;
    }

    sql += ` 
      GROUP BY s.utm_source
      ORDER BY count DESC
    `;

    const rows = await MySQL2Service.query(sql, params);
    return rows.map((row) => {
      const utmSourceId = row?._id;
      const count = Number(row?.count || 0);
      return {
        _id: utmSourceId,
        count: count,
      };
    });
  } catch (error) {
    logger.error('Error getting users by UTM source:', error);
    throw error;
  }
}

/**
 * Get users grouped by UTM content
 * @param {Object} dateConditions - Date filter conditions
 */
async function getUsersByUtmContent(dateConditions) {
  try {
    const { whereClause, params } = MySQL2Service.buildWhereClause(dateConditions);

    let sql = `
      SELECT 
        s.utm_content as _id,
        COUNT(*) as count
      FROM ${SignUpSourceModel.tableName} s
      INNER JOIN ${UserModel.tableName} u ON s.user_id = u.user_id
    `;

    if (whereClause !== '1') {
      // Replace field names with table alias for user table
      const userWhereClause = whereClause.replace(/create_time/g, 'u.create_time');
      sql += ` WHERE ${userWhereClause} AND s.utm_content IS NOT NULL AND s.utm_content != ''`;
    } else {
      sql += ` WHERE s.utm_content IS NOT NULL AND s.utm_content != ''`;
    }

    sql += ` 
      GROUP BY s.utm_content
      ORDER BY count DESC
    `;

    const rows = await MySQL2Service.query(sql, params);
    return rows.map((row) => {
      const utmContentId = row?._id;
      const count = Number(row?.count || 0);
      return {
        _id: utmContentId,
        count: count,
      };
    });
  } catch (error) {
    logger.error('Error getting users by UTM content:', error);
    throw error;
  }
}
