# JX1 Kiếm Hiệp Tình 1 - Dịch Vụ Phân Tích

Dịch vụ API Node.js dành cho phân tích trò chơ<PERSON>, đư<PERSON><PERSON> xây dựng với Express và MongoDB, với xác thực JWT.

## Tính Năng

- **JavaScript Hiện Đại**: Cú pháp ES6+ với biên dịch Babel
- **Framework API**: Express.js cho các điểm cuối REST API
- **Cơ Sở Dữ Liệu**: MongoDB với Mongoose ODM
- **Xác Thực**: Dựa trên JWT với Passport.js
- **Ghi Log**: Ghi log toàn diện với Winston và Express-Winston
- **Xử Lý Lỗi**: Xử lý lỗi tập trung với báo cáo lỗi đẹp mắt
- **Bảo Mật**: Helmet cho HTTP headers, hỗ trợ CORS
- **Hệ Thống Build**: Webpack cho việc đóng gói và tối ưu hóa
- **Trải Nghiệm Phát Triển**: Hot reloading với Nodemon
- **Định Dạng Mã**: ESLint và Prettier cho mã sạch và nhất quán

## Cấu Trúc Dự Án

```
.
├── dist/                        # Compiled output (not in repo)
├── logs/                        # Application logs
│   ├── combined.log             # Combined logs
│   └── error.log                # Error logs only
├── .vscode/
│   └── settings.json            # VS Code editor settings
├── src/                         # Source code
│   ├── index.js                 # Application entry point
│   ├── config/                  # Configuration files
│   │   ├── database.js          # MongoDB connection setup
│   │   ├── express.js           # Express configuration
│   │   ├── logger.js            # Winston logger configuration
│   │   └── passport.js          # Passport JWT authentication setup
│   ├── controllers/
│   │   └── userController.js    # User API controllers
│   ├── middleware/
│   │   └── authMiddleware.js    # JWT authentication middleware
│   ├── models/
│   │   └── saboId/              # Models for sabo-id database
│   │       ├── userModel.js     # User schema
│   │       ├── profileModel.js  # User profile schema
│   │       └── signUpSourceModel.js # User signup tracking schema
│   ├── routes/
│   │   ├── index.js             # Main router configuration
│   │   └── userRoute.js         # User-related routes
│   ├── services/
│   │   ├── errorService.js      # Custom error handling
│   │   ├── logService.js        # Error logging service
│   │   └── userService.js       # User analytics service
│   └── utils/
│       ├── apiResponseUltil.js  # Standardized API responses
│       ├── jwtGeneratorUtil.js  # JWT token generation
│       └── timeUtil.js          # Date/time utility functions
├── .babelrc                     # Babel configuration
├── .env                         # Environment variables
├── .env.example                 # Example environment configuration
├── .eslintignore                # Files to ignore for linting
├── .eslintrc.json               # ESLint configuration
├── .gitignore                   # Git ignore rules
├── .prettierignore              # Files to ignore for formatting
├── .prettierrc.json             # Prettier code formatter config
├── package.json                 # NPM dependencies and scripts
├── webpack.config.js            # Webpack build configuration
└── README.md                    # Project documentation
```

## Yêu Cầu Hệ Thống

- Node.js (khuyến nghị v14.x hoặc mới hơn)
- MongoDB (cục bộ hoặc từ xa)

## Cài Đặt

1. Sao chép kho lưu trữ:

   ```bash
   <NAME_EMAIL>:sabogame-develop/jx1-kiemhieptinh1-analytic-service.git
   cd jx1-kiemhieptinh1-analytic-service
   ```

2. Cài đặt các gói phụ thuộc:

   ```bash
   npm install
   ```

3. Thiết lập biến môi trường:
   ```bash
   cp .env.example .env
   ```
   Sau đó chỉnh sửa `.env` với cấu hình cụ thể của bạn:
   - `NODE_ENV`: Chế độ môi trường (development, production)
   - `PORT`: Cổng mà server sẽ lắng nghe
   - `MONGODB_URI`: Chuỗi kết nối MongoDB của bạn
   - `JWT_SECRET`: Khóa bí mật để ký JWT
   - `MONGOOSE_DEBUG`: Bật debug truy vấn MongoDB (tùy chọn)
   - `MONGOOSE_SERVER_SELECTION_TIMEOUT`: Thời gian timeout khi chọn server MongoDB
   - `MONGOOSE_SOCKET_TIMEOUT`: Thời gian timeout cho socket MongoDB
   - `SERVER_TIMEOUT`: Thời gian timeout cho Express server

## Phát Triển

Chạy máy chủ phát triển với tính năng hot reloading:

```bash
npm run dev
```

Điều này sẽ:

- Xây dựng dự án ở chế độ phát triển với webpack
- Theo dõi thay đổi tệp và xây dựng lại
- Tự động khởi động lại máy chủ khi mã nguồn thay đổi

## Định Dạng và Kiểm Tra Mã

Dự án sử dụng ESLint và Prettier để đảm bảo chất lượng mã. Bạn có thể chạy các lệnh sau:

```bash
# Sửa lỗi ESLint
npm run lint

# Định dạng mã với Prettier
npm run format
```

Cấu hình:

- ESLint: Xem `.eslintrc.json` cho các quy tắc kiểm tra mã
- Prettier: Xem `.prettierrc.json` cho quy tắc định dạng

Dự án cũng được cấu hình với git hooks (thông qua simple-git-hooks) để tự động kiểm tra và định dạng mã trước khi commit.

## Xây Dựng Cho Môi Trường Production

Xây dựng dự án cho môi trường production:

```bash
npm run build
```

Điều này tạo ra một gói tối ưu hóa trong thư mục `dist`.

## Chạy Ở Chế Độ Production

Khởi động máy chủ ở chế độ production:

```bash
npm start
```

## Xác Thực API

Tất cả các yêu cầu API đến các điểm cuối `/api/*` đều yêu cầu xác thực bằng JWT token:

```
Authorization: Bearer <your_jwt_token>
```

JWT token được xác thực bởi middleware xác thực sử dụng Passport.js.

## Xử Lý Lỗi

Ứng dụng bao gồm hệ thống xử lý lỗi toàn diện:

- Xử lý lỗi tập trung để phản hồi nhất quán
- Định dạng lỗi đẹp mắt trong môi trường phát triển
- Tích hợp Sentry tùy chọn để theo dõi lỗi trong môi trường production
- Log lỗi có cấu trúc

## Định Dạng Phản Hồi API

Tất cả phản hồi API tuân theo định dạng nhất quán:

### Phản Hồi Thành Công:

```json
{
  "success": true,
  "message": "Thao tác thành công",
  "data": {
    // Dữ liệu phản hồi
  }
}
```

### Phản Hồi Lỗi:

```json
{
  "success": false,
  "message": "Thông báo lỗi",
  "data": {
    // Chi tiết lỗi tùy chọn
  },
  "stack": "Stack trace lỗi (chỉ trong môi trường phát triển)"
}
```

## Script Phát Triển

- `npm run build` - Xây dựng cho môi trường production
- `npm run build:dev` - Xây dựng cho môi trường phát triển
- `npm start` - Khởi động máy chủ production
- `npm run dev` - Khởi động máy chủ phát triển với hot reloading
- `npm run lint` - Kiểm tra và sửa lỗi ESLint
- `npm run format` - Định dạng mã với Prettier

## Giấy Phép

ISC
