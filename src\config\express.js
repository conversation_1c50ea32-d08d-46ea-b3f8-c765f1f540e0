import express from 'express';
import morgan from 'morgan';
import compression from 'compression';
import cors from 'cors';
import helmet from 'helmet';
import expressStatusMonitor from 'express-status-monitor';
import expressWinston from 'express-winston';
import logger from './logger.js';

/**
 * Express configuration
 */
const configureExpress = (app) => {
  // Cho phép xử lý JSON request body
  app.use(express.json());

  // Security headers
  app.use(helmet());

  // Enable CORS
  app.use(cors());

  // Parse requests body
  app.use(express.json());
  app.use(express.urlencoded({ extended: true }));

  // Gzip compression
  app.use(compression());

  // Request logging with morgan - use 'dev' format for development
  app.use(morgan(process.env.NODE_ENV === 'production' ? 'combined' : 'dev'));

  // Express Winston request logging
  app.use(
    expressWinston.logger({
      winstonInstance: logger,
      meta: true,
      msg: 'HTTP {{req.method}} {{req.url}} {{res.statusCode}} {{res.responseTime}}ms',
      expressFormat: true,
      colorize: process.env.NODE_ENV !== 'production',
    })
  );

  // Express Status Monitor - only in development
  if (process.env.NODE_ENV !== 'production') {
    app.use(expressStatusMonitor());
  }

  // Error logging with express-winston
  app.use(
    expressWinston.errorLogger({
      winstonInstance: logger,
    })
  );

  return app;
};

export default configureExpress;
