import { Model } from 'sequelize';

export default class BaseModel extends Model {
  static init(sequelize, DataTypes) {
    const defaultOptions = {
      sequelize: sequelize,
      modelName: this.name,
      tableName: this.tableName,
      timestamps: true,
      paranoid: true,
      underscored: true,
      freezeTableName: true,
    };

    const customOptions = this.getOptions() || {};
    const options = Object.assign({}, defaultOptions, customOptions);

    return super.init(this.getFields(DataTypes), options);
  }

  static get tableName() {
    throw new Error('tableName must be defined in child class');
  }

  static getFields(DataTypes) {
    return {
      id: {
        type: DataTypes.BIGINT,
        primaryKey: true,
        autoIncrement: true,
        comment: 'ID chính',
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        field: 'created_at',
        comment: 'Thời gian tạo',
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        field: 'updated_at',
        comment: 'Thời gian cập nhật',
      },
      deletedAt: {
        type: DataTypes.DATE,
        allowNull: true,
        field: 'deleted_at',
        comment: 'Thời gian xóa',
      },
    };
  }

  static getBaseFields() {
    return ['id', 'created_at', 'updated_at', 'deleted_at'];
  }

  static getOptions() {
    return {
      indexes: [{ fields: ['created_at'] }],
      tableOptions: {
        engine: 'InnoDB',
        charset: 'utf8mb4',
        collate: 'utf8mb4_unicode_ci',
      },
    };
  }

  /**
   * Validation dữ liệu cơ bản
   * @param {Object} data - Dữ liệu cần validate
   */
  static validate(data) {
    if (!data || typeof data !== 'object') {
      throw new Error('Data must be an object');
    }
    return true;
  }

  /**
   * Transform dữ liệu từ database
   * @param {Object} row - Dữ liệu từ DB
   */
  static transform(row) {
    if (!row) return null;

    // Có thể override trong class con để transform data
    return row;
  }

  /**
   * Tìm theo ID (Sequelize)
   */
  static async findById(id, options = {}) {
    const record = await this.findByPk(id, options);
    if (!record) {
      throw new Error(`${this.name} với ID ${id} không tồn tại`);
    }
    return record;
  }

  static async create(data, options = {}) {
    try {
      return await super.create(data, options);
    } catch (error) {
      throw new Error(`Lỗi tạo ${this.name}: ${error.message}`);
    }
  }

  static async update(data, options = {}) {
    try {
      return await super.update(data, options);
    } catch (error) {
      throw new Error(`Lỗi cập nhật ${this.name}: ${error.message}`);
    }
  }

  static async delete(options = {}) {
    try {
      return await this.destroy(options);
    } catch (error) {
      throw new Error(`Lỗi xóa ${this.name}: ${error.message}`);
    }
  }

  static async count(options = {}) {
    try {
      return await super.count(options);
    } catch (error) {
      throw new Error(`Lỗi đếm ${this.name}: ${error.message}`);
    }
  }

  static async findAll(options = {}) {
    try {
      return await super.findAll(options);
    } catch (error) {
      throw new Error(`Lỗi tìm ${this.name}: ${error.message}`);
    }
  }

  static async findOne(options = {}) {
    try {
      return await super.findOne(options);
    } catch (error) {
      throw new Error(`Lỗi tìm ${this.name}: ${error.message}`);
    }
  }

  static async paginate(page = 1, limit = 10, options = {}) {
    const offset = (page - 1) * limit;
    const query = Object.assign({}, options, {
      limit: limit,
      offset: offset,
    });
    const { count, rows } = await this.findAndCountAll(query);

    return {
      data: rows,
      pagination: {
        page,
        limit,
        total: count,
        totalPages: Math.ceil(count / limit),
        hasNext: page < Math.ceil(count / limit),
        hasPrev: page > 1,
      },
    };
  }
}
