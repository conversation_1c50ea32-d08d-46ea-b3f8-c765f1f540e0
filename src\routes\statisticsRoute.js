import express from 'express';
import {
  getDailyStatistics,
  getRevenueStatistics,
  getOverviewStats,
  getOverviewByDate,
  getTopRecharge,
  getTopPackages,
  getRecentHistory,
} from '../controllers/overviewController.js';

const router = express.Router();

router.get('/overview', getOverviewStats);

router.get('/overview/by-date', getOverviewByDate);

router.get('/daily', getDailyStatistics);

router.get('/revenue', getRevenueStatistics);

router.get('/top-recharge', getTopRecharge);

router.get('/top-packages', getTopPackages);

router.get('/recent-history', getRecentHistory);

export default router;
