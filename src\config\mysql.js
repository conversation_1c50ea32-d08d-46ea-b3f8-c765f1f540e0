import mysql from 'mysql2/promise';
import logger from './logger.js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Check required environment variables
if (
  !process.env.MYSQL_USER ||
  process.env.MYSQL_PASSWORD === undefined ||
  !process.env.MYSQL_DATABASE
) {
  logger.error('❌ Lỗi: <PERSON>hiếu biến môi trường MySQL (MYSQL_USER, MYSQL_PASSWORD, MYSQL_DATABASE)');
  throw new Error('Missing required MySQL environment variables');
}

// MySQL connection configuration
const mysqlConfig = {
  host: process.env.MYSQL_HOST || 'localhost',
  port: Number(process.env.MYSQL_PORT) || 3306,
  user: process.env.MYSQL_USER,
  password: process.env.MYSQL_PASSWORD,
  database: process.env.MYSQL_DATABASE,
  charset: 'utf8mb4',
  timezone: '+07:00',
  connectionLimit: Number(process.env.MYSQL_CONNECTION_LIMIT) || 10,
  multipleStatements: true,
  dateStrings: true,
  supportBigNumbers: true,
  bigNumberStrings: true,
  // Pool configuration
  acquireTimeout: Number(process.env.MYSQL_ACQUIRE_TIMEOUT) || 60000,
  waitForConnections: true,
  queueLimit: 0,
  enableKeepAlive: true,
  keepAliveInitialDelay: 0,
};

// Create MySQL connection pool
let mysqlPool;

try {
  mysqlPool = mysql.createPool(mysqlConfig);
  logger.info('🔧 Đã tạo MySQL connection pool');
} catch (err) {
  logger.error('❌ Lỗi tạo MySQL connection pool:', err);
  throw err;
}

// Test the connection immediately
(async () => {
  try {
    const connection = await mysqlPool.getConnection();
    logger.info('Đã kết nối tới MySQL database thành công');
    connection.release();
  } catch (err) {
    logger.error('❌ Lỗi kết nối MySQL:', err);
    // Không throw error để app vẫn có thể chạy
  }
})();

// Handle graceful shutdown
const shutdownHandler = async () => {
  try {
    if (mysqlPool) {
      await mysqlPool.end();
      logger.info('✅ Đã đóng kết nối MySQL');
    }
  } catch (error) {
    logger.error('❌ Lỗi khi đóng kết nối MySQL:', error);
    throw error;
  }
};

// Register process signal handlers
process.on('SIGINT', shutdownHandler);
process.on('SIGTERM', shutdownHandler);

export { mysqlPool };
