import logger from './logger.js';
import { mysqlPool } from './mysql.js';
import { mysql2Pool } from './mysql2.js';

// Database connection manager
class DatabaseManager {
  constructor() {
    this.mysql = mysqlPool;
    this.mysql2 = mysql2Pool; // Sabo ID MySQL replacement
  }

  // Get MySQL connection pool
  getMySQL() {
    return this.mysql;
  }

  // Get MySQL2 connection pool (Sabo ID replacement)
  getMySQL2() {
    return this.mysql2;
  }

  // Health check for all databases
  async healthCheck() {
    const health = {
      mysql: false,
      mysql2: false, // Sabo ID MySQL replacement
      timestamp: new Date().toISOString(),
    };

    try {
      // Check MySQL
      const connection = await this.mysql.getConnection();
      connection.release();
      health.mysql = true;

      // Check MySQL2 (Sabo ID replacement)
      const connection2 = await this.mysql2.getConnection();
      connection2.release();
      health.mysql2 = true;
    } catch (error) {
      logger.error('Database health check failed:', error);
    }

    return health;
  }

  // Graceful shutdown
  async shutdown() {
    try {
      logger.info('🔄 Đang đóng kết nối database...');

      // Close MySQL connection pool
      if (this.mysql) {
        await this.mysql.end();
      }

      // Close MySQL2 connection pool (Sabo ID replacement)
      if (this.mysql2) {
        await this.mysql2.end();
      }

      logger.info('✅ Đã đóng tất cả kết nối database');
    } catch (error) {
      logger.error('❌ Lỗi khi đóng kết nối database:', error);
      throw error; // Re-throw to be handled by the signal handler
    }
  }
}

// Create database manager instance
const dbManager = new DatabaseManager();

// Initial health check after a short delay
setTimeout(async () => {
  try {
    const health = await dbManager.healthCheck();
    logger.info('✅ Database Health Check:', health);
  } catch (error) {
    logger.error('❌ Database health check failed:', error);
  }
}, 2000);

// Handle graceful shutdown
const shutdownHandler = async () => {
  try {
    await dbManager.shutdown();
  } catch (error) {
    logger.error('❌ Lỗi trong quá trình tắt ứng dụng:', error);
    throw error;
  }
};

// Register process signal handlers
process.on('SIGINT', shutdownHandler);
process.on('SIGTERM', shutdownHandler);

export { dbManager, mysqlPool, mysql2Pool };
