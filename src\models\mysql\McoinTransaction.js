import BaseModel from '../shared/BaseModel.js';
import { Enums } from '../../config/constants.js';
import { Fields, Queries } from '../shared/helpers.js';

export default class McoinTransaction extends BaseModel {
  static get tableName() {
    return 'mcoin_transaction';
  }

  static getFields(DataTypes) {
    return Object.assign({}, super.getFields(DataTypes), {
      // Transaction information
      transactionId: {
        type: DataTypes.STRING(255),
        allowNull: false,
        field: 'transaction_id',
        comment: 'Transaction ID',
      },

      // Game information
      gameId: {
        type: DataTypes.BIGINT,
        allowNull: false,
        field: 'game_id',
        comment: 'ID game',
      },

      // Account information
      accountId: Fields.accountId(DataTypes),
      accountName: Fields.accountName(DataTypes),

      // Order information
      orderId: {
        type: DataTypes.STRING(255),
        allowNull: false,
        field: 'order_id',
        comment: 'Order ID',
      },

      // Amount information
      amount: Fields.amount(DataTypes),
      coinAmount: Fields.coinAmount(DataTypes),

      // Payment method
      paymentMethod: Fields.paymentMethod(DataTypes),

      // Status
      status: Fields.status(DataTypes),

      // Additional data
      metaData: Fields.metaData(DataTypes),
    });
  }

  static getOptions() {
    return Object.assign({}, super.getOptions(), {
      indexes: [
        {
          fields: ['transaction_id'],
        },
        {
          fields: ['game_id'],
        },
        {
          fields: ['account_id'],
        },
        {
          fields: ['order_id'],
          unique: true,
        },
        {
          fields: ['status'],
        },
        {
          fields: ['payment_method'],
        },
        {
          fields: ['created_at'],
        },
        {
          fields: ['account_id', 'game_id'],
        },
        {
          fields: ['account_id', 'status'],
        },
        {
          fields: ['game_id', 'status'],
        },
      ],
    });
  }

  /**
   * Get transaction status enum
   * @returns {Object} Status enum
   */
  static getStatusEnum() {
    return Enums.STATUS;
  }

  /**
   * Get payment method enum
   * @returns {Object} Payment method enum
   */
  static getPaymentMethodEnum() {
    return Enums.PAYMENT_METHOD;
  }

  // Scope methods using shared Queries
  static scopePending() {
    return Queries.byStatus(Enums.STATUS.PENDING);
  }

  static scopeCompleted() {
    return Queries.byStatus(Enums.STATUS.COMPLETED);
  }

  static scopeFailed() {
    return Queries.byStatus(Enums.STATUS.FAILED);
  }

  static scopeCancelled() {
    return Queries.byStatus(Enums.STATUS.CANCELLED);
  }

  static scopeByAccount(accountId) {
    return Queries.byAccount(accountId);
  }

  static scopeByGame(gameId) {
    return { where: { gameId } };
  }

  static scopeByPaymentMethod(paymentMethod) {
    return Queries.byStatus(paymentMethod);
  }

  static scopeByOrder(orderId) {
    return { where: { orderId } };
  }

  static scopeByAmountRange(minAmount, maxAmount) {
    return {
      where: {
        amount: {
          [this.sequelize.Op.between]: [minAmount, maxAmount],
        },
      },
    };
  }

  static scopeToday() {
    return Queries.today();
  }

  static scopeThisMonth() {
    return Queries.thisMonth();
  }
}
