import { Sequelize } from 'sequelize';

// Import shared BaseModel
import BaseModel from './BaseModel.js';

// Import MySQL models
import Transaction from '../mysql/Transaction.js';
import HistoryTransaction from '../mysql/HistoryTransaction.js';
import McoinTransaction from '../mysql/McoinTransaction.js';
import AccountWallet from '../mysql/AccountWallet.js';
import AccountCard from '../mysql/AccountCard.js';
import AccountLog from '../mysql/AccountLog.js';

// Import MySQL2 models
import UserModel from '../mysql2/userModel.js';
import ProfileModel from '../mysql2/profileModel.js';
import SignUpSourceModel from '../mysql2/signUpSourceModel.js';

/**
 * Initialize Sequelize models (MySQL với Sequelize)
 * @param {Sequelize} sequelize - Sequelize instance
 * @returns {Object} Object containing all initialized Sequelize models
 */
export function initializeSequelizeModels(sequelize) {
  const { DataTypes } = Sequelize;

  try {
    // Initialize all Sequelize models
    const models = {
      Transaction: Transaction.init(sequelize, DataTypes),
      HistoryTransaction: HistoryTransaction.init(sequelize, DataTypes),
      McoinTransaction: McoinTransaction.init(sequelize, DataTypes),
      AccountWallet: AccountWallet.init(sequelize, DataTypes),
      AccountCard: AccountCard.init(sequelize, DataTypes),
      AccountLog: AccountLog.init(sequelize, DataTypes),
    };

    // Set up associations
    Object.values(models).forEach((model) => {
      if (model.associate) {
        model.associate(models);
      }
    });

    console.log('✅ All Sequelize models initialized successfully');
    return models;
  } catch (error) {
    console.error('❌ Error initializing Sequelize models:', error.message);
    throw new Error(`Failed to initialize Sequelize models: ${error.message}`);
  }
}

/**
 * Initialize MySQL2 models (Raw MySQL2 connection)
 * @returns {Object} Object containing all MySQL2 model classes
 */
export function initializeMySQL2Models() {
  try {
    // MySQL2 models không cần initialize như Sequelize
    const models = {
      UserModel,
      ProfileModel,
      SignUpSourceModel,
    };

    // Set up associations nếu có
    Object.values(models).forEach((model) => {
      if (model.associate) {
        model.associate(models);
      }
    });

    console.log('✅ All MySQL2 models initialized successfully');
    return models;
  } catch (error) {
    console.error('❌ Error initializing MySQL2 models:', error.message);
    throw new Error(`Failed to initialize MySQL2 models: ${error.message}`);
  }
}

/**
 * Initialize all models (cả Sequelize và MySQL2)
 * @param {Sequelize} sequelize - Sequelize instance (optional)
 * @returns {Object} Object containing all models
 */
export function initializeAllModels(sequelize = null) {
  try {
    const allModels = {};

    // Initialize MySQL2 models (luôn luôn có)
    const mysql2Models = initializeMySQL2Models();
    Object.assign(allModels, { mysql2: mysql2Models });

    // Initialize Sequelize models (nếu có sequelize instance)
    if (sequelize) {
      const sequelizeModels = initializeSequelizeModels(sequelize);
      Object.assign(allModels, { sequelize: sequelizeModels });
    }

    console.log('✅ All models initialized successfully');
    return allModels;
  } catch (error) {
    console.error('❌ Error initializing all models:', error.message);
    throw new Error(`Failed to initialize all models: ${error.message}`);
  }
}

/**
 * Sync Sequelize models with database
 * @param {Sequelize} sequelize - Sequelize instance
 * @param {Object} options - Sync options
 */
export async function syncSequelizeModels(sequelize, options = {}) {
  try {
    const models = initializeSequelizeModels(sequelize);

    const syncOptions = Object.assign(
      {
        force: false, // Set to true to drop and recreate tables
        alter: false, // Set to true to alter existing tables
      },
      options
    );

    await sequelize.sync(syncOptions);
    console.log('✅ All Sequelize models synchronized with database');

    return models;
  } catch (error) {
    console.error('❌ Error syncing Sequelize models:', error.message);
    throw new Error(`Failed to sync Sequelize models: ${error.message}`);
  }
}

/**
 * Get all model classes (not initialized)
 * @returns {Object} Object containing all model classes
 */
export function getModelClasses() {
  return {
    // Shared
    BaseModel,

    // Sequelize models
    sequelize: {
      Transaction,
      HistoryTransaction,
      McoinTransaction,
      AccountWallet,
      AccountCard,
      AccountLog,
    },

    // MySQL2 models
    mysql2: {
      UserModel,
      ProfileModel,
      SignUpSourceModel,
    },
  };
}

/**
 * Validate model associations
 * @param {Object} models - Initialized models
 * @param {string} type - Model type ('sequelize' or 'mysql2')
 * @returns {boolean} Validation result
 */
export function validateAssociations(models, type = 'sequelize') {
  try {
    const modelEntries = Object.entries(models);
    console.log(`🔍 Validating ${type} associations for ${modelEntries.length} models...`);

    // Check if all models have proper associations/structure
    for (const [modelName, model] of modelEntries) {
      if (type === 'sequelize') {
        if (model && model.associations) {
          console.log(`  ✅ ${modelName}: ${Object.keys(model.associations).length} associations`);
        } else {
          console.log(`  ⚠️  ${modelName}: No associations defined`);
        }
      } else if (type === 'mysql2') {
        if (model && model.tableName) {
          console.log(`  ✅ ${modelName}: table '${model.tableName}'`);
        } else {
          console.log(`  ⚠️  ${modelName}: No tableName defined`);
        }
      }
    }

    return true;
  } catch (error) {
    console.error(`❌ Error validating ${type} associations:`, error.message);
    return false;
  }
}

/**
 * Get Sequelize model statistics
 * @param {Object} models - Initialized Sequelize models
 * @returns {Object} Model statistics
 */
export async function getSequelizeModelStats(models) {
  try {
    const stats = {};

    for (const [modelName, model] of Object.entries(models)) {
      try {
        const count = await model.count();
        Object.defineProperty(stats, modelName, {
          value: {
            recordCount: count,
            tableName: model.tableName,
            associations: Object.keys(model.associations || {}).length,
          },
          writable: true,
          enumerable: true,
        });
      } catch (error) {
        Object.defineProperty(stats, modelName, {
          value: {
            error: error.message,
            tableName: model.tableName,
          },
          writable: true,
          enumerable: true,
        });
      }
    }

    return stats;
  } catch (error) {
    console.error('❌ Error getting Sequelize model stats:', error.message);
    throw new Error(`Failed to get Sequelize model stats: ${error.message}`);
  }
}

/**
 * Test database connection and models
 * @param {Sequelize} sequelize - Sequelize instance (optional)
 * @returns {Promise<boolean>} Test result
 */
export async function testConnection(sequelize = null) {
  try {
    console.log('🔍 Testing models...');

    // Initialize MySQL2 models
    const mysql2Models = initializeMySQL2Models();
    validateAssociations(mysql2Models, 'mysql2');

    // Test Sequelize if provided
    if (sequelize) {
      console.log('🔍 Testing Sequelize database connection...');

      // Test connection
      await sequelize.authenticate();
      console.log('✅ Sequelize database connection successful');

      // Initialize Sequelize models
      const sequelizeModels = initializeSequelizeModels(sequelize);

      // Validate associations
      validateAssociations(sequelizeModels, 'sequelize');

      // Get model stats
      const stats = await getSequelizeModelStats(sequelizeModels);
      console.log('📊 Sequelize model statistics:', stats);
    }

    console.log('✅ All model tests completed successfully');
    return true;
  } catch (error) {
    console.error('❌ Model connection test failed:', error.message);
    return false;
  }
}

// Export model classes
export {
  BaseModel,

  // Sequelize models
  Transaction,
  HistoryTransaction,
  McoinTransaction,
  AccountWallet,
  AccountCard,
  AccountLog,

  // MySQL2 models
  UserModel,
  ProfileModel,
  SignUpSourceModel,
};

// Export helpers
export * from './helpers.js';
