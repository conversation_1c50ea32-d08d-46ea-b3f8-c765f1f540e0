import HTTPStatus from 'http-status';
import logger from '../config/logger.js';
import { errorResponse } from '../utils/apiResponseUltil.js';

const serverAuthMiddleware = (req, res, next) => {
  try {
    const serverSecretKey = process.env.SERVER_SECRET_KEY || 'your-default-secret-key';

    const apiKey =
      req.headers['x-api-key'] ||
      req.headers['authorization']?.replace('Bearer ', '') ||
      req.headers['authorization'];

    if (!apiKey) {
      logger.warn('Yêu cầu thiếu API key:', {
        ip: req.ip,
        url: req.originalUrl,
        method: req.method,
      });
      return res
        .status(HTTPStatus.UNAUTHORIZED)
        .json(errorResponse('Thiếu API key. Sử dụng header X-API-Key hoặc Authorization.'));
    }

    if (apiKey !== serverSecretKey) {
      logger.warn('API key không hợp lệ:', {
        ip: req.ip,
        url: req.originalUrl,
        method: req.method,
        providedKey: apiKey.substring(0, 8) + '...',
      });
      return res.status(HTTPStatus.UNAUTHORIZED).json(errorResponse('API key không hợp lệ.'));
    }

    logger.info('Server-to-server request authenticated:', {
      ip: req.ip,
      url: req.originalUrl,
      method: req.method,
    });

    next();
  } catch (error) {
    logger.error('Lỗi trong middleware xác thực:', error);
    return res.status(HTTPStatus.INTERNAL_SERVER_ERROR).json(errorResponse('Lỗi xác thực server.'));
  }
};

export default serverAuthMiddleware;
