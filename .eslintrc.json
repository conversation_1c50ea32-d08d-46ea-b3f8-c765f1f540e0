{"root": true, "env": {"node": true, "es2021": true, "browser": false}, "parserOptions": {"ecmaVersion": "latest", "sourceType": "module"}, "extends": ["eslint:recommended", "plugin:node/recommended", "plugin:promise/recommended", "plugin:prettier/recommended"], "plugins": ["prettier", "promise", "security"], "globals": {"module": "readonly", "require": "readonly", "__dirname": "readonly", "process": "readonly"}, "rules": {"prettier/prettier": ["error"], "no-console": "off", "node/no-unsupported-features/es-syntax": ["error", {"ignores": ["modules"]}], "node/no-missing-import": "error", "node/no-missing-require": "error", "node/no-unpublished-import": "off", "node/no-unpublished-require": "off", "node/no-unsupported-features/node-builtins": "off", "security/detect-object-injection": ["error", {"enableDangerousObjectInjection": false}], "security/detect-unsafe-regex": "warn"}, "overrides": [{"files": ["**/*.js"], "rules": {"node/no-unsupported-features/es-syntax": "off"}}, {"files": ["ecosystem.config.js"], "parserOptions": {"sourceType": "script"}, "rules": {"node/no-unsupported-features/es-syntax": "off", "node/no-unsupported-features/node-builtins": "off"}}]}