# MySQL Helper - Hướng dẫn sử dụng nâng cao

## 🚀 Tổng quan

MySQL Helper đã được tối ưu với nhiều tính năng nâng cao để sử dụng linh hoạt cho nhiều mục đích khác nhau:

- ✅ **Query Builder** - Xây dựng query linh hoạt
- ✅ **Batch Operations** - Xử lý dữ liệu hàng loạt
- ✅ **Transaction với Retry** - Xử lý lỗi tự động
- ✅ **Validation** - Kiểm tra dữ liệu đầu vào
- ✅ **Debug Mode** - Theo dõi query thực thi
- ✅ **Connection Pool** - Quản lý kết nối hiệu quả

## 📖 Cách sử dụng cơ bản

### 1. Import MySQL Helper

```javascript
import MySQLHelper from '../services/mysqlHelper.js';
```

### 2. Query đơn giản

```javascript
// Query cơ bản
const users = await MySQLHelper.query('SELECT * FROM users WHERE active = ?', [true]);

// Query với debug
const users = await MySQLHelper.query('SELECT * FROM users', [], { debug: true });
```

### 3. CRUD Operations

```javascript
// INSERT với nhiều tùy chọn
const result = await MySQLHelper.insert(
  'users',
  {
    username: 'john_doe',
    email: '<EMAIL>',
    password: 'hashed_password',
  },
  {
    ignore: true, // Bỏ qua lỗi duplicate
    onDuplicateUpdate: { updated_at: new Date() }, // Update nếu duplicate
    returnFields: 'id, username', // Trả về fields cụ thể
  }
);

// UPDATE với validation
const updateResult = await MySQLHelper.update('users', { full_name: 'John Smith' }, 'id = ?', [1], {
  returnFields: 'id, full_name',
});

// DELETE với tùy chọn
const deleteResult = await MySQLHelper.delete('users', 'id = ?', [1], {
  limit: 1,
  returnFields: 'id',
});
```

## 🔧 Tính năng nâng cao

### 1. Phân trang nâng cao

```javascript
// Phân trang cơ bản
const result = await MySQLHelper.paginate('users', 1, 10, 'active = ?', [true]);

// Phân trang với JOIN và GROUP BY
const result = await MySQLHelper.paginate(
  'users u',
  1,
  10,
  'u.active = ?',
  [true],
  'u.created_at DESC',
  {
    select: 'u.*, COUNT(p.id) as post_count',
    joins: 'LEFT JOIN posts p ON u.id = p.user_id',
    groupBy: 'u.id',
    having: 'post_count > 0',
    debug: true,
  }
);

console.log(result);
// {
//   data: [...],
//   pagination: {
//     page: 1,
//     limit: 10,
//     total: 100,
//     totalPages: 10,
//     hasNext: true,
//     hasPrev: false,
//     startIndex: 1,
//     endIndex: 10
//   },
//   meta: {
//     query: "SELECT u.*, COUNT(p.id) as post_count FROM users u LEFT JOIN posts p ON u.id = p.user_id WHERE u.active = ? GROUP BY u.id HAVING post_count > 0 ORDER BY u.created_at DESC LIMIT ? OFFSET ?",
//     executionTime: 1640995200000
//   }
// }
```

### 2. Batch Operations

```javascript
// Insert nhiều bản ghi cùng lúc
const users = [
  { username: 'user1', email: '<EMAIL>' },
  { username: 'user2', email: '<EMAIL>' },
  { username: 'user3', email: '<EMAIL>' },
];

const result = await MySQLHelper.insertMany('users', users, {
  ignore: true,
  onDuplicateUpdate: { updated_at: new Date() },
});

// Batch insert với chunk size
const largeDataArray = [
  /* 10000 records */
];
const results = await MySQLHelper.batchInsert('users', largeDataArray, {
  batchSize: 1000,
  ignore: true,
});
```

### 3. Transaction với Retry

```javascript
// Transaction cơ bản
const result = await MySQLHelper.transaction(async (connection) => {
  // Tạo user
  const [userResult] = await connection.execute(
    'INSERT INTO users (username, email) VALUES (?, ?)',
    ['john', '<EMAIL>']
  );

  // Tạo profile
  await connection.execute('INSERT INTO user_profiles (user_id, bio) VALUES (?, ?)', [
    userResult.insertId,
    'Hello world!',
  ]);

  return userResult.insertId;
});

// Transaction với retry options
const result = await MySQLHelper.transaction(
  async (connection) => {
    // Logic transaction
  },
  {
    maxRetries: 5,
    retryDelay: 2000,
  }
);
```

### 4. Query Builder Helpers

```javascript
// Build WHERE clause từ object
const conditions = {
  active: true,
  role: 'user',
  created_at: '2024-01-01',
};

const { clause, params } = MySQLHelper.buildWhereClause(conditions, 'AND');
// clause: "active = ? AND role = ? AND created_at = ?"
// params: [true, 'user', '2024-01-01']

// Build ORDER BY clause
const orderBy = MySQLHelper.buildOrderByClause('created_at', 'DESC');
// "created_at DESC"

// Sử dụng trong query
const users = await MySQLHelper.getAll(
  `SELECT * FROM users WHERE ${clause} ORDER BY ${orderBy}`,
  params
);
```

### 5. Advanced Count

```javascript
// Count cơ bản
const total = await MySQLHelper.count('users', 'active = ?', [true]);

// Count với DISTINCT
const uniqueEmails = await MySQLHelper.count('users', 'active = ?', [true], {
  distinct: 'email',
  alias: 'unique_count',
});

// Count với custom alias
const totalUsers = await MySQLHelper.count('users', '1', [], {
  alias: 'user_count',
});
```

## 🛠️ Validation và Error Handling

### 1. Required Records

```javascript
// Throw error nếu không tìm thấy record
const user = await MySQLHelper.getOne('SELECT * FROM users WHERE id = ?', [999], {
  required: true,
});
// Error: "Record not found for query: SELECT * FROM users WHERE id = ?"
```

### 2. Input Validation

```javascript
// Validation tự động trong insert/update
try {
  await MySQLHelper.insert('users', {}); // Error: Table name and data are required
  await MySQLHelper.update('users', {}, 'id = ?', [1]); // Error: Table name and update data are required
  await MySQLHelper.delete('users', '', []); // Error: Table name and where condition are required
} catch (error) {
  console.error(error.message);
}
```

### 3. Debug Mode

```javascript
// Bật debug để theo dõi query
const users = await MySQLHelper.query('SELECT * FROM users WHERE active = ?', [true], {
  debug: true,
});

// Log output:
// 🔍 MySQL Query: SELECT * FROM users WHERE active = ? { params: [true] }
// ✅ MySQL Query Result: 5 rows
```

## 📊 Performance Optimization

### 1. Connection Pool Info

```javascript
// Lấy thông tin connection pool
const poolInfo = MySQLHelper.getPoolInfo();
console.log(poolInfo);
// {
//   threadId: 123,
//   config: { host: 'localhost', port: 3306, ... },
//   pool: { ... }
// }
```

### 2. Raw Query với Connection riêng

```javascript
// Sử dụng connection riêng cho query phức tạp
const result = await MySQLHelper.rawQuery(
  'SELECT * FROM users WHERE MATCH(username, email) AGAINST(? IN BOOLEAN MODE)',
  ['john'],
  { debug: true }
);
```

### 3. Test Connection

```javascript
// Kiểm tra kết nối với timeout
const isConnected = await MySQLHelper.testConnection(5000);
if (isConnected) {
  console.log('✅ MySQL connection is healthy');
} else {
  console.log('❌ MySQL connection failed');
}
```

## 🔒 Security Features

### 1. SQL Injection Prevention

```javascript
// Luôn sử dụng parameters
const user = await MySQLHelper.getOne(
  'SELECT * FROM users WHERE username = ? AND password = ?',
  [username, password] // ✅ Safe
);

// KHÔNG làm thế này
const user = await MySQLHelper.getOne(
  `SELECT * FROM users WHERE username = '${username}'` // ❌ Dangerous
);
```

### 2. Escape Values

```javascript
// Escape string values
const safeValue = MySQLHelper.escape("O'Connor");
// "O''Connor"
```

## 📝 Ví dụ thực tế

### 1. User Management System

```javascript
class UserService {
  static async createUser(userData) {
    return await MySQLHelper.transaction(async (connection) => {
      // Tạo user
      const [userResult] = await connection.execute(
        'INSERT INTO users (username, email, password) VALUES (?, ?, ?)',
        [userData.username, userData.email, userData.password]
      );

      // Tạo profile
      await connection.execute(
        'INSERT INTO user_profiles (user_id, full_name, bio) VALUES (?, ?, ?)',
        [userResult.insertId, userData.fullName, userData.bio || '']
      );

      // Tạo default settings
      await connection.execute(
        'INSERT INTO user_settings (user_id, theme, notifications) VALUES (?, ?, ?)',
        [userResult.insertId, 'light', true]
      );

      return userResult.insertId;
    });
  }

  static async getUsersWithPagination(page = 1, limit = 10, filters = {}) {
    const { clause, params } = MySQLHelper.buildWhereClause(filters);
    const orderBy = MySQLHelper.buildOrderByClause('created_at', 'DESC');

    return await MySQLHelper.paginate('users u', page, limit, clause, params, orderBy, {
      select: 'u.*, up.full_name, up.bio, COUNT(p.id) as post_count',
      joins:
        'LEFT JOIN user_profiles up ON u.id = up.user_id LEFT JOIN posts p ON u.id = p.user_id',
      groupBy: 'u.id',
      debug: process.env.NODE_ENV === 'development',
    });
  }
}
```

### 2. Analytics System

```javascript
class AnalyticsService {
  static async getDailyStats(date) {
    const result = await MySQLHelper.paginate(
      'user_activities ua',
      1,
      1000,
      'DATE(ua.created_at) = ?',
      [date],
      'ua.created_at DESC',
      {
        select: `
          DATE(ua.created_at) as date,
          COUNT(*) as total_activities,
          COUNT(DISTINCT ua.user_id) as unique_users,
          AVG(ua.duration) as avg_duration
        `,
        groupBy: 'DATE(ua.created_at)',
        debug: true,
      }
    );

    return result.data[0] || null;
  }

  static async batchInsertActivities(activities) {
    return await MySQLHelper.batchInsert('user_activities', activities, {
      batchSize: 500,
      ignore: true,
    });
  }
}
```

## ⚡ Tips và Best Practices

1. **Luôn sử dụng parameters** để tránh SQL injection
2. **Sử dụng debug mode** trong development để theo dõi query
3. **Sử dụng transaction** cho các thao tác phức tạp
4. **Sử dụng batch operations** cho dữ liệu lớn
5. **Validate input** trước khi thực hiện query
6. **Sử dụng connection pool** hiệu quả
7. **Monitor performance** với debug mode
8. **Handle errors** một cách graceful
