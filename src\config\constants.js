// Application constants and environment variables configuration

// Server configuration
export const SERVER = {
  PORT: process.env.PORT || 3000,
  ENV: process.env.NODE_ENV || 'development',
  TIMEOUT: parseInt(process.env.SERVER_TIMEOUT, 10) || 30000,
  SECRET_KEY: process.env.SERVER_SECRET_KEY || 'your-default-secret-key',
};

// Database configuration
export const DATABASE = {
  // MySQL
  MYSQL: {
    HOST: process.env.MYSQL_HOST || 'localhost',
    PORT: Number(process.env.MYSQL_PORT) || 3306,
    USER: process.env.MYSQL_USER,
    PASSWORD: process.env.MYSQL_PASSWORD,
    DATABASE: process.env.MYSQL_DATABASE,
    CONNECTION_LIMIT: Number(process.env.MYSQL_CONNECTION_LIMIT) || 10,
    ACQUIRE_TIMEOUT: Number(process.env.MYSQL_ACQUIRE_TIMEOUT) || 60000,
  },

  // MySQL2 (Sabo ID replacement)
  MYSQL2: {
    HOST: process.env.MYSQL2_HOST || 'localhost',
    PORT: Number(process.env.MYSQL2_PORT) || 5307,
    USER: process.env.MYSQL2_USER,
    PASSWORD: process.env.MYSQL2_PASSWORD,
    DATABASE: process.env.MYSQL2_DATABASE,
    CONNECTION_LIMIT: Number(process.env.MYSQL2_CONNECTION_LIMIT) || 10,
    ACQUIRE_TIMEOUT: Number(process.env.MYSQL2_ACQUIRE_TIMEOUT) || 60000,
  },
};

// Time Periods for statistics
export const TIME_PERIODS = {
  ALL_TIME: 0,
  TODAY: 1,
  YESTERDAY: 2,
  THIS_WEEK: 3,
  LAST_7_DAYS: 4,
  THIS_MONTH: 5,
  LAST_MONTH: 6,
};

// Data Types for statistics
export const DATA_TYPES = {
  ACCOUNTS: 1,
  IPS: 2,
  REVENUE: 3,
  TRANSACTIONS: 4,
};

// Statistics Types with Vietnamese descriptions
export const STATISTIC_TYPES = {
  REGISTERED_ACCOUNTS: 1, // Số tài khoản đăng ký
  REGISTERED_IPS: 2, // Số IP đăng ký
  TOTAL_REVENUE: 3, // Tổng doanh thu
  TRANSACTION_COUNT: 4, // Số lượng giao dịch
};

// Error handling
export const ERROR_MESSAGES = {
  INVALID_DATA_TYPE: 'Loại dữ liệu không hợp lệ. Phải là 1-4.',
  DB_CONNECTION_ERROR: 'Lỗi kết nối cơ sở dữ liệu',
  UNAUTHORIZED: 'Không có quyền truy cập',
};

// Logging
export const LOGGING = {
  LEVEL: process.env.NODE_ENV === 'production' ? 'info' : 'debug',
  SENTRY_DSN: process.env.RAVEN_ID,
};

// Enums for various status and type values
export const Enums = {
  // Từ mysql
  STATUS: {
    PENDING: 0,
    COMPLETED: 1,
    FAILED: 2,
    CANCELLED: 3,
  },
  PAYMENT_METHOD: {
    UNKNOWN: 0,
    BANK: 1,
    CARD: 2,
    WALLET: 3,
    CRYPTO: 4,
  },
  ACTION: {
    UNKNOWN: 0,
    LOGIN: 1,
    LOGOUT: 2,
    TRANSACTION: 3,
    PROFILE_UPDATE: 4,
    SECURITY_CHANGE: 5,
  },

  // Từ mysql2
  USER_STATUS: {
    INACTIVE: 0,
    ACTIVE: 1,
    BANNED: 2,
    DELETED: 3,
  },
  PROFILE_STATUS: {
    INCOMPLETE: 0,
    COMPLETE: 1,
    VERIFIED: 2,
  },
  SIGNUP_SOURCE: {
    UNKNOWN: 0,
    WEBSITE: 1,
    MOBILE_APP: 2,
    SOCIAL_MEDIA: 3,
    REFERRAL: 4,
  },
  GENDER: {
    UNKNOWN: 0,
    MALE: 1,
    FEMALE: 2,
    OTHER: 3,
  },
  BOOLEAN: {
    FALSE: 0,
    TRUE: 1,
  },
};

// Mcoin packages configuration
export const MCOIN_PACKAGES = {
  20: { id: 1, category: 'Mcoin', packageName: 'Gói 1', denomination: 20 },
  50: { id: 2, category: 'Mcoin', packageName: 'Gói 2', denomination: 50 },
  100: { id: 3, category: 'Mcoin', packageName: 'Gói 3', denomination: 100 },
  200: { id: 4, category: 'Mcoin', packageName: 'Gói 4', denomination: 200 },
  500: { id: 5, category: 'Mcoin', packageName: 'Gói 5', denomination: 500 },
  1000: { id: 6, category: 'Mcoin', packageName: 'Gói 6', denomination: 1000 },
};

// Get all available package amounts
export const AVAILABLE_PACKAGE_AMOUNTS = Object.keys(MCOIN_PACKAGES).map(Number);

export const isProduction = SERVER.ENV === 'production';
export const isDevelopment = SERVER.ENV === 'development';
