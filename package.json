{"name": "jx1-**********************-service", "version": "1.0.0", "description": "To make it easy for you to get started with GitLab, here's a list of recommended next steps.", "main": "src/index.js", "type": "module", "engines": {"node": "22.14.0"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "babel src --out-dir dist --copy-files", "build:dev": "babel src --out-dir dist --copy-files --source-maps", "start": "node dist/index.js", "dev": "nodemon --exec babel-node src/index.js", "lint": "eslint src --fix", "format": "prettier --write 'src/**/*.js'", "prepare": "npx simple-git-hooks", "postinstall": "git config core.hooksPath .git/hooks", "prod": "npm run build && pm2 start ecosystem.config.json --env production", "prod:stop": "pm2 stop jx1_kiemhieptinh1_analytic_service", "prod:restart": "npm run build && pm2 restart ecosystem.config.json --env production", "prod:status": "pm2 status", "prod:logs": "pm2 logs jx1_kiemhieptinh1_analytic_service"}, "simple-git-hooks": {"pre-commit": "npx lint-staged"}, "lint-staged": {"*.js": ["eslint --fix", "prettier --write"], "*.{json,md}": ["prettier --write"]}, "repository": {"type": "git", "url": "git+ssh://**************/sabogame-develop/jx1-**********************-service.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://gitlab.com/sabogame-develop/jx1-**********************-service/issues"}, "homepage": "https://gitlab.com/sabogame-develop/jx1-**********************-service#readme", "dependencies": {"@babel/runtime": "^7.23.8", "compression": "^1.8.0", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^5.1.0", "express-status-monitor": "^1.3.4", "express-winston": "^4.2.0", "helmet": "^8.1.0", "http-status": "^2.1.0", "jsonwebtoken": "^9.0.2", "moment": "^2.30.1", "moment-timezone": "^0.5.48", "morgan": "^1.10.0", "mysql2": "^3.9.2", "pm2": "^5.3.1", "pretty-error": "^4.0.0", "raven": "^2.6.4", "sequelize": "^6.37.1", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"@babel/cli": "^7.26.10", "@babel/core": "^7.26.10", "@babel/preset-env": "^7.26.9", "@babel/node": "^7.26.0", "@types/jsonwebtoken": "^9.0.10", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-promise": "^7.2.1", "eslint-plugin-security": "^3.0.1", "lint-staged": "^15.2.2", "nodemon": "^3.1.9", "prettier": "^3.2.5", "simple-git-hooks": "^2.10.0"}}