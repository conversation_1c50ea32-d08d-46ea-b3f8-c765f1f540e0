import BaseModel from '../shared/BaseModel.js';
import { Enums } from '../../config/constants.js';
import { Fields, Utils } from '../shared/helpers.js';
import MySQL2Service from '../../services/mysql2Service.js';

export default class ProfileModel extends BaseModel {
  static get tableName() {
    return 'profile';
  }

  static getFields() {
    return Object.assign({}, super.getBaseFields(), {
      user_id: Fields.userId(),
      first_name: {
        type: 'VARCHAR(100)',
        allowNull: true,
        field: 'first_name',
        comment: 'Tên',
      },
      last_name: {
        type: 'VARCHAR(100)',
        allowNull: true,
        field: 'last_name',
        comment: 'H<PERSON>',
      },
      birth_date: {
        type: 'DATE',
        allowNull: true,
        field: 'birth_date',
        comment: '<PERSON><PERSON><PERSON> sinh',
      },
      gender: {
        type: 'TINYINT',
        allowNull: false,
        defaultValue: 0,
        comment: '<PERSON>iới tính',
      },
      avatar: {
        type: 'VARCHAR(255)',
        allowNull: true,
        comment: 'Avatar URL',
      },
      bio: {
        type: 'TEXT',
        allowNull: true,
        comment: 'Tiểu sử',
      },
      status: Fields.status(),
    });
  }

  static getOptions() {
    return Object.assign({}, super.getOptions(), {
      indexes: [
        { fields: ['user_id'], unique: true },
        { fields: ['status'] },
        { fields: ['gender'] },
        { fields: ['birth_date'] },
        { fields: ['user_id', 'status'] },
      ],
    });
  }

  static getStatusEnum() {
    return Enums.PROFILE_STATUS;
  }

  static getGenderEnum() {
    return Enums.GENDER;
  }

  static transform(row) {
    if (!row) return null;

    return {
      ...row,
      first_name: Utils.sanitizeString(row.first_name),
      last_name: Utils.sanitizeString(row.last_name),
      bio: Utils.sanitizeString(row.bio),
      full_name: row.first_name && row.last_name ? `${row.first_name} ${row.last_name}` : null,
      age: row.birth_date ? ProfileModel.calculateAge(row.birth_date) : null,
    };
  }

  static calculateAge(birthDate) {
    if (!birthDate) return null;

    const today = new Date();
    const birth = new Date(birthDate);
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--;
    }

    return age;
  }

  static validate(data) {
    super.validate(data);

    if (data.birth_date) {
      const birthDate = new Date(data.birth_date);
      const today = new Date();

      if (birthDate >= today) {
        throw new Error('Birth date must be in the past');
      }

      const age = ProfileModel.calculateAge(data.birth_date);
      if (age < 13 || age > 120) {
        throw new Error('Age must be between 13 and 120');
      }
    }

    if (data.gender !== undefined && !Object.values(Enums.GENDER).includes(data.gender)) {
      throw new Error('Invalid gender value');
    }

    return true;
  }

  static async findByUserId(userId) {
    return await MySQL2Service.findById(this.tableName, 'user_id', userId, this.transform);
  }

  static async findComplete(options = {}) {
    return await MySQL2Service.find(
      this.tableName,
      { status: Enums.PROFILE_STATUS.COMPLETE },
      options,
      this.transform
    );
  }

  static async findVerified(options = {}) {
    return await MySQL2Service.find(
      this.tableName,
      { status: Enums.PROFILE_STATUS.VERIFIED },
      options,
      this.transform
    );
  }

  static async estimatedDocumentCount() {
    return await MySQL2Service.estimatedDocumentCount(this.tableName);
  }

  static async countDocuments(conditions = {}) {
    return await MySQL2Service.countDocuments(this.tableName, conditions);
  }

  static async countComplete() {
    return await MySQL2Service.countDocuments(this.tableName, {
      status: Enums.PROFILE_STATUS.COMPLETE,
    });
  }

  static async countVerified() {
    return await MySQL2Service.countDocuments(this.tableName, {
      status: Enums.PROFILE_STATUS.VERIFIED,
    });
  }

  static async countByStatus(status) {
    return await MySQL2Service.countDocuments(this.tableName, { status });
  }
}
