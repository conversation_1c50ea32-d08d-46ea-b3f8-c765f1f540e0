# Chế độ môi trường (development, production)
NODE_ENV=development
# Cổng mà server sẽ lắng nghe
PORT=3000
# Database Mysql
MYSQL_HOST=127.0.0.1
MYSQL_USER=root
MYSQL_PASSWORD=
MYSQL_DATABASE=id.mboxvn.com
MYSQL_CONNECTION_LIMIT=10

# Database Mysql 2 (Sabo ID replacement for MongoDB)
MYSQL2_HOST=*************
MYSQL2_PORT=5307
MYSQL2_USER=jx-bq-slave
MYSQL2_PASSWORD=IGBAsuhlGasLOuas
MYSQL2_DATABASE=sabo-id
MYSQL2_CONNECTION_LIMIT=10
# Khóa bí mật để ký và xác thực "server to server"
SERVER_SECRET_KEY=1i6rKlzhi9t1yCI5Jt360qPgLaoUIC6KUbl0MK7V8l6bQfwegF8lmM1ODoEBX0H4
# Thời gian timeout cho Express (mili giây)
SERVER_TIMEOUT=60000