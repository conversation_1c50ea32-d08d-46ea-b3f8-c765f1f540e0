import moment from 'moment';
import logger from '../config/logger.js';
import { UserModel } from '../models/shared/index.js';
import { Enums } from '../config/constants.js';
import MySQLService from './mysqlService.js';
import {
  TIME_PERIODS,
  DATA_TYPES,
  ERROR_MESSAGES,
  STATISTIC_TYPES,
  MCOIN_PACKAGES,
  AVAILABLE_PACKAGE_AMOUNTS,
} from '../config/constants.js';

export { TIME_PERIODS, DATA_TYPES };

export const getOverview = async () => {
  try {
    const [totalAccounts, distinctIPs, transactionStats] = await Promise.all([
      UserModel.estimatedDocumentCount(),
      UserModel.distinct('ip'),
      MySQLService.query(
        'SELECT COALESCE(SUM(amount), 0) AS totalRevenue, COUNT(*) AS totalTransactions FROM transactions WHERE status = ?',
        [Enums.STATUS.COMPLETED]
      ),
    ]);

    const [transactionRow] = transactionStats;
    const validIPs = distinctIPs.filter((ip) => ip && ip !== '');

    return {
      totalAccounts: Number(totalAccounts) || 0,
      totalUniqueIPs: validIPs.length,
      totalRevenue: Number(transactionRow && transactionRow.totalRevenue) || 0,
      totalSuccessfulTransactions: Number(transactionRow && transactionRow.totalTransactions) || 0,
    };
  } catch (error) {
    logger.error('statisticService.getOverview error:', error);
    throw new Error(`Failed to get overview statistics: ${error.message}`);
  }
};

/**
 * Tính toán khoảng thời gian trước đó dựa trên khoảng thời gian hiện tại
 * @param {string} startDate - Ngày bắt đầu (YYYY-MM-DD)
 * @param {string} endDate - Ngày kết thúc (YYYY-MM-DD)
 * @param {number} timePeriod - Loại khoảng thời gian (1-5)
 * @returns {Object} Đối tượng chứa previousStartDate và previousEndDate (YYYY-MM-DD)
 */
const getPreviousDateRange = (startDate, endDate, timePeriod) => {
  const startMoment = moment(startDate);
  const endMoment = moment(endDate);
  const getDaysDiff = () => endMoment.diff(startMoment, 'days') + 1;

  const periodHandlers = {
    [TIME_PERIODS.TODAY]: () => ({
      previousStartDate: moment(startDate).subtract(1, 'day'),
      previousEndDate: moment(endDate).subtract(1, 'day'),
    }),

    [TIME_PERIODS.YESTERDAY]: () => ({
      previousStartDate: moment(startDate).subtract(1, 'day'),
      previousEndDate: moment(endDate).subtract(1, 'day'),
    }),

    [TIME_PERIODS.THIS_WEEK]: () => ({
      previousStartDate: moment(startDate).subtract(7, 'days'),
      previousEndDate: moment(endDate).subtract(7, 'days'),
    }),

    [TIME_PERIODS.LAST_7_DAYS]: () => ({
      previousStartDate: moment(startDate).subtract(7, 'days'),
      previousEndDate: moment(endDate).subtract(7, 'days'),
    }),

    [TIME_PERIODS.THIS_MONTH]: () => {
      const lastMonth = moment(startDate).subtract(1, 'months');
      return {
        previousStartDate: lastMonth.clone().startOf('month'),
        previousEndDate: lastMonth.clone().endOf('month'),
      };
    },

    default: () => {
      const daysDiff = getDaysDiff();
      return {
        previousStartDate: moment(startDate).subtract(daysDiff, 'days'),
        previousEndDate: moment(endDate).subtract(daysDiff, 'days'),
      };
    },
  };

  // Use a whitelist of allowed timePeriod values
  const validTimePeriods = Object.values(TIME_PERIODS);
  const handler = validTimePeriods.includes(timePeriod)
    ? periodHandlers[timePeriod] // eslint-disable-line security/detect-object-injection
    : periodHandlers.default;

  const { previousStartDate, previousEndDate } = handler();

  return {
    previousStartDate: previousStartDate.format('YYYY-MM-DD'),
    previousEndDate: previousEndDate.format('YYYY-MM-DD'),
  };
};

/**
 * Tính toán ngày bắt đầu và kết thúc dựa trên time period
 * @param {number} timePeriod - Khoảng thời gian (TIME_PERIODS constants)
 * @returns {Object} Đối tượng chứa startDate và endDate (YYYY-MM-DD)
 */
const getCurrentDateRange = (timePeriod) => {
  const now = moment();

  const periodHandlers = {
    [TIME_PERIODS.TODAY]: () => ({
      startDate: now.format('YYYY-MM-DD'),
      endDate: now.format('YYYY-MM-DD'),
    }),

    [TIME_PERIODS.YESTERDAY]: () => {
      const yesterday = now.clone().subtract(1, 'day');
      return {
        startDate: yesterday.format('YYYY-MM-DD'),
        endDate: yesterday.format('YYYY-MM-DD'),
      };
    },

    [TIME_PERIODS.THIS_WEEK]: () => ({
      startDate: now.clone().startOf('week').format('YYYY-MM-DD'),
      endDate: now.clone().endOf('week').format('YYYY-MM-DD'),
    }),

    [TIME_PERIODS.LAST_7_DAYS]: () => ({
      startDate: now.clone().subtract(6, 'days').format('YYYY-MM-DD'),
      endDate: now.format('YYYY-MM-DD'),
    }),

    [TIME_PERIODS.THIS_MONTH]: () => ({
      startDate: now.clone().startOf('month').format('YYYY-MM-DD'),
      endDate: now.clone().endOf('month').format('YYYY-MM-DD'),
    }),
  };

  // Use whitelist to prevent object injection
  const validTimePeriods = Object.values(TIME_PERIODS);
  if (!validTimePeriods.includes(timePeriod)) {
    throw new Error('Invalid time period');
  }

  const handler = periodHandlers[timePeriod]; // eslint-disable-line security/detect-object-injection
  if (typeof handler !== 'function') {
    throw new Error('Invalid time period');
  }

  return handler();
};

const calculateGrowth = (currentValue, previousValue) => {
  // Xử lý trường hợp mẫu số bằng 0
  if (previousValue === 0) {
    return {
      value: currentValue > 0 ? 100 : 0,
      isGrowth: currentValue > 0,
    };
  }

  const growth = ((currentValue - previousValue) / previousValue) * 100;
  const roundedGrowth = Math.round(growth * 100) / 100;

  return {
    value: roundedGrowth,
    isGrowth: growth >= 0,
  };
};

export const getOverviewByDate = async (params) => {
  try {
    const { type, time_period = TIME_PERIODS.TODAY } = params;

    // Tính toán ngày hiện tại dựa trên time_period
    const { startDate, endDate } = getCurrentDateRange(time_period);

    const currentValue = await getValueByType(type, startDate, endDate);
    const { previousStartDate, previousEndDate } = getPreviousDateRange(
      startDate,
      endDate,
      time_period
    );

    const previousValue = await getValueByType(type, previousStartDate, previousEndDate);

    // Tính toán phần trăm tăng trưởng
    const { value: growthPercentage, isGrowth } = calculateGrowth(currentValue, previousValue);

    return {
      total: currentValue,
      previousValue,
      growthPercentage,
      isGrowth,
      period: {
        startDate,
        endDate,
        timePeriod: time_period,
      },
    };
  } catch (error) {
    logger.error('Lỗi khi lấy tổng quan thống kê:', error);
    throw new Error(`Không thể lấy dữ liệu thống kê: ${error.message}`);
  }
};

const getAccountCount = async (startTimestamp, endTimestamp) =>
  UserModel.countDocuments({
    createTime: { $gte: startTimestamp, $lte: endTimestamp },
  });

const getUniqueIPCount = async (startTimestamp, endTimestamp) => {
  const distinctIPs = await UserModel.distinct('ip', {
    createTime: { $gte: startTimestamp, $lte: endTimestamp },
    ip: { $exists: true, $nin: [null, ''] },
  });
  return distinctIPs.length;
};

const getTotalRevenue = async (startDate, endDate) => {
  const [revenueResult] = await MySQLService.query(
    `SELECT COALESCE(SUM(amount), 0) AS total
     FROM transactions
     WHERE status = ? AND DATE(created_at) BETWEEN ? AND ?`,
    [Enums.STATUS.COMPLETED, startDate, endDate]
  );
  return Number(revenueResult?.total) || 0;
};

const getTransactionCount = async (startDate, endDate) => {
  const [transactionResult] = await MySQLService.query(
    `SELECT COUNT(*) AS total
     FROM transactions
     WHERE status = ? AND DATE(created_at) BETWEEN ? AND ?`,
    [Enums.STATUS.COMPLETED, startDate, endDate]
  );
  return Number(transactionResult?.total) || 0;
};

async function getValueByType(type, startDate, endDate) {
  const startMoment = moment(startDate).startOf('day');
  const endMoment = moment(endDate).endOf('day');
  const startTimestamp = startMoment.unix();
  const endTimestamp = endMoment.unix();

  const validTypeHandlers = {
    [DATA_TYPES.ACCOUNTS]: () => getAccountCount(startTimestamp, endTimestamp),
    [DATA_TYPES.IPS]: () => getUniqueIPCount(startTimestamp, endTimestamp),
    [DATA_TYPES.REVENUE]: () => getTotalRevenue(startDate, endDate),
    [DATA_TYPES.TRANSACTIONS]: () => getTransactionCount(startDate, endDate),
  };

  const handler = validTypeHandlers[type]; // eslint-disable-line security/detect-object-injection
  if (typeof handler !== 'function') {
    throw new Error(ERROR_MESSAGES.INVALID_DATA_TYPE);
  }

  return await handler();
}

export const getDailyStatistics = async (params) => {
  try {
    const { start_date, end_date, type } = params;

    // Convert dates using moment
    const startMoment = moment(start_date);
    const endMoment = moment(end_date);

    // Generate date range array using moment
    const dateRange = [];
    const currentMoment = moment(startMoment);
    while (currentMoment.isSameOrBefore(endMoment)) {
      dateRange.push(currentMoment.format('YYYY-MM-DD'));
      currentMoment.add(1, 'day');
    }

    // Get daily statistics
    const dailyStats = [];
    let totalAccounts = 0;
    let totalUniqueIPs = 0;
    const allIPs = new Set();

    for (const date of dateRange) {
      const dayStart = moment(date).startOf('day').unix();
      const dayEnd = moment(date).endOf('day').unix();

      const dayStats = { date };

      if (!type || type === STATISTIC_TYPES.REGISTERED_ACCOUNTS) {
        // Get accounts for this day
        const accountCount = await UserModel.countDocuments({
          createTime: { $gte: dayStart, $lte: dayEnd },
        });
        dayStats.registered_accounts = accountCount;
        totalAccounts += accountCount;
      }

      if (!type || type === STATISTIC_TYPES.REGISTERED_IPS) {
        // Get unique IPs for this day
        const dayIPs = await UserModel.distinct('ip', {
          createTime: { $gte: dayStart, $lte: dayEnd },
          ip: { $exists: true, $nin: [null, ''] },
        });
        dayStats.registered_ips = dayIPs.length;
        dayIPs.forEach((ip) => allIPs.add(ip));
      }

      // Calculate clone coefficient if both accounts and IPs are present
      if (dayStats.registered_accounts && dayStats.registered_ips && dayStats.registered_ips > 0) {
        dayStats.clone_coefficient = Number(
          (dayStats.registered_accounts / dayStats.registered_ips).toFixed(3)
        );
      }

      dailyStats.push(dayStats);
    }

    totalUniqueIPs = allIPs.size;

    // Build response based on type filter
    const response = { daily_stats: dailyStats };

    if (!type) {
      // Both accounts and IPs
      response.total_summary = {
        total_registered_accounts: totalAccounts,
        total_registered_ips: totalUniqueIPs,
      };
    } else if (type === STATISTIC_TYPES.REGISTERED_ACCOUNTS) {
      // Accounts only
      response.total_summary = {
        total_registered_accounts: totalAccounts,
      };
    } else if (type === STATISTIC_TYPES.REGISTERED_IPS) {
      // IPs only
      response.total_summary = {
        total_registered_ips: totalUniqueIPs,
      };
    }

    return response;
  } catch (error) {
    logger.error('statisticService.getDailyStatistics error:', error);
    throw new Error(`Failed to get daily statistics: ${error.message}`);
  }
};

export const getRevenueStatistics = async (params) => {
  try {
    const { start_date, end_date } = params;

    // Get daily revenue data
    const revenueResult = await MySQLService.query(
      `SELECT 
         DATE(created_at) as date,
         COALESCE(SUM(amount), 0) as revenue
       FROM transactions 
       WHERE status = ? AND DATE(created_at) BETWEEN ? AND ?
       GROUP BY DATE(created_at)
       ORDER BY DATE(created_at)`,
      [Enums.STATUS.COMPLETED, start_date, end_date]
    );

    // Get total revenue for the period
    const totalResult = await MySQLService.query(
      'SELECT COALESCE(SUM(amount), 0) AS totalRevenue FROM transactions WHERE status = ? AND DATE(created_at) BETWEEN ? AND ?',
      [Enums.STATUS.COMPLETED, start_date, end_date]
    );

    // Format daily revenue data
    const dailyRevenue = revenueResult.map((row) => ({
      date: row.date,
      revenue: Number(row.revenue) || 0,
    }));

    return {
      daily_revenue: dailyRevenue,
      total_revenue: Number(totalResult[0]?.totalRevenue) || 0,
    };
  } catch (error) {
    logger.error('statisticService.getRevenueStatistics error:', error);
    throw new Error(`Failed to get revenue statistics: ${error.message}`);
  }
};

/**
 * Get time range conditions for SQL query based on time period
 * @param {number} timePeriod - Time period constant from TIME_PERIODS
 * @returns {Object} Object containing whereClause and params for SQL query
 */
const getTimeRangeForTopRecharge = (timePeriod) => {
  const now = moment();

  switch (timePeriod) {
    case TIME_PERIODS.TODAY: {
      return {
        whereClause: ' AND DATE(t.created_at) = ?',
        params: [now.format('YYYY-MM-DD')],
      };
    }

    case TIME_PERIODS.YESTERDAY: {
      return {
        whereClause: ' AND DATE(t.created_at) = ?',
        params: [now.clone().subtract(1, 'day').format('YYYY-MM-DD')],
      };
    }

    case TIME_PERIODS.LAST_7_DAYS: {
      return {
        whereClause: ' AND DATE(t.created_at) >= ?',
        params: [now.clone().subtract(6, 'days').format('YYYY-MM-DD')],
      };
    }

    case TIME_PERIODS.THIS_WEEK: {
      return {
        whereClause: ' AND DATE(t.created_at) >= ? AND DATE(t.created_at) <= ?',
        params: [
          now.clone().startOf('week').format('YYYY-MM-DD'),
          now.clone().endOf('week').format('YYYY-MM-DD'),
        ],
      };
    }

    case TIME_PERIODS.THIS_MONTH: {
      return {
        whereClause: ' AND DATE(t.created_at) >= ? AND DATE(t.created_at) <= ?',
        params: [
          now.clone().startOf('month').format('YYYY-MM-DD'),
          now.clone().endOf('month').format('YYYY-MM-DD'),
        ],
      };
    }

    case TIME_PERIODS.LAST_MONTH: {
      const lastMonth = now.clone().subtract(1, 'month');
      return {
        whereClause: ' AND DATE(t.created_at) >= ? AND DATE(t.created_at) <= ?',
        params: [
          lastMonth.clone().startOf('month').format('YYYY-MM-DD'),
          lastMonth.clone().endOf('month').format('YYYY-MM-DD'),
        ],
      };
    }

    case TIME_PERIODS.ALL_TIME:
    default: {
      return {
        whereClause: '',
        params: [],
      };
    }
  }
};

export const getTopRecharge = async (params) => {
  try {
    const { page, limit, time_period } = params;
    const offset = (page - 1) * limit;

    const { whereClause, params: timeParams } = getTimeRangeForTopRecharge(time_period);

    let optimizedQuery;
    let queryParams;

    if (time_period === TIME_PERIODS.ALL_TIME) {
      optimizedQuery = `
        SELECT 
          t.account_id as id,
          t.account_name as accountName,
          SUM(t.amount) as totalAmount,
          MAX(t.created_at) as lastRechargeDate
        FROM transactions t
        WHERE t.status = 1
        GROUP BY t.account_id, t.account_name
      `;
      queryParams = [];
    } else {
      optimizedQuery = `
        SELECT 
          t.account_id as id,
          t.account_name as accountName,
          SUM(t.amount) as totalAmount,
          MAX(t.created_at) as lastRechargeDate
        FROM transactions t
        WHERE t.status = ?${whereClause}
        GROUP BY t.account_id, t.account_name
      `;
      queryParams = [Enums.STATUS.COMPLETED, ...timeParams];
    }

    logger.info(`Executing super optimized query (time_period: ${time_period}):`, {
      queryLength: optimizedQuery.length,
      hasTimeFilter: time_period !== TIME_PERIODS.ALL_TIME,
    });

    const dataResult = await MySQLService.query(optimizedQuery, queryParams);

    const processedData = dataResult
      .map((row) => ({
        id: row.id,
        accountName: row.accountName,
        totalAmount: Number(row.totalAmount) || 0,
        userId: row.id,
        lastRechargeDate: row.lastRechargeDate,
      }))
      .sort((a, b) => b.totalAmount - a.totalAmount)
      .slice(offset, offset + limit);

    const total = dataResult.length;
    const lastPage = Math.ceil(total / limit);

    const pagination = {
      current_page: page,
      per_page: limit,
      total,
      last_page: lastPage,
      from: total > 0 ? offset + 1 : 0,
      to: Math.min(offset + limit, total),
      has_more_pages: page < lastPage,
    };

    return {
      data: processedData,
      pagination,
    };
  } catch (error) {
    logger.error('statisticService.getTopRecharge error:', error);
    throw new Error(`Failed to get top recharge statistics: ${error.message}`);
  }
};

export const getRecentHistory = async (params) => {
  try {
    const { page = 1, limit = 20 } = params;
    const offset = (page - 1) * limit;

    const query = `SELECT 
        id,
        account_name,
        server_name,
        amount,
        transaction_id,
        status,
        created_at
      FROM mcoin_transaction
      WHERE status = 1
      ORDER BY created_at DESC`;

    logger.info('Executing getRecentHistory query:', {
      queryLength: query.length,
    });

    const dataResult = await MySQLService.query(query, []);

    const processedData = dataResult
      .map((row) => ({
        id: row.id,
        account: row.account_name,
        server: row.server_name,
        denomination: Number(row.amount) || 0,
        transactionId: row.transaction_id,
        status: row.status,
        createdAt: row.created_at,
      }))
      .slice(offset, offset + limit);

    const total = dataResult.length;
    const lastPage = Math.ceil(total / limit);

    const pagination = {
      page: Number(page),
      limit: Number(limit),
      total,
      lastPage,
      hasNext: page < lastPage,
      hasPrev: page > 1,
    };

    return {
      data: processedData,
      pagination,
    };
  } catch (error) {
    logger.error('statisticService.getRecentHistory error:', error);
    throw new Error(`Failed to get recent history: ${error.message}`);
  }
};

export const getTopPackages = async () => {
  try {
    const query = `SELECT 
        amount,
        COUNT(*) as purchaseCount,
        SUM(amount) as totalRevenue
      FROM mcoin_transaction
      WHERE status = 1 
        AND amount IN (${AVAILABLE_PACKAGE_AMOUNTS.join(', ')})
      GROUP BY amount
      ORDER BY purchaseCount DESC`;

    logger.info('Executing getTopPackages query');

    const dataResult = await MySQLService.query(query, []);

    const totalPurchases = dataResult.reduce(
      (sum, row) => sum + (Number(row.purchaseCount) || 0),
      0
    );
    const totalRevenue = dataResult.reduce((sum, row) => sum + (Number(row.totalRevenue) || 0), 0);

    const processedData = dataResult
      .map((row) => {
        const amount = Number(row.amount);
        const purchaseCount = Number(row.purchaseCount) || 0;
        const revenue = Number(row.totalRevenue) || 0;

        let packageInfo = null;
        if (AVAILABLE_PACKAGE_AMOUNTS.includes(amount)) {
          switch (amount) {
            case 20:
              packageInfo = MCOIN_PACKAGES[20];
              break;
            case 50:
              packageInfo = MCOIN_PACKAGES[50];
              break;
            case 100:
              packageInfo = MCOIN_PACKAGES[100];
              break;
            case 200:
              packageInfo = MCOIN_PACKAGES[200];
              break;
            case 500:
              packageInfo = MCOIN_PACKAGES[500];
              break;
            case 1000:
              packageInfo = MCOIN_PACKAGES[1000];
              break;
            default:
              packageInfo = null;
          }
        }
        if (!packageInfo) return null;

        return {
          id: packageInfo.id,
          category: packageInfo.category,
          denomination: packageInfo.denomination,
          purchaseCount: purchaseCount,
          revenue: revenue,
          packageName: packageInfo.packageName,
          percentage:
            totalPurchases > 0 ? Number(((purchaseCount / totalPurchases) * 100).toFixed(1)) : 0,
        };
      })
      .filter((item) => item !== null);

    return {
      data: processedData,
      summary: {
        totalPackages: processedData.length,
        totalRevenue: totalRevenue,
        totalPurchases: totalPurchases,
      },
    };
  } catch (error) {
    logger.error('statisticService.getTopPackages error:', error);
    throw new Error(`Failed to get top packages statistics: ${error.message}`);
  }
};
