import moment from 'moment-timezone';

// Set default timezone to Vietnam (UTC+7)
moment.tz.setDefault('Asia/Ho_Chi_Minh');

/**
 * Formats a timestamp to a specific format in Vietnam timezone
 * @param {Number|Date|String} date - Date to format (timestamp, Date object, or date string)
 * @param {String} format - Output format (default: 'YYYY-MM-DD HH:mm:ss')
 * @returns {String} Formatted date string
 */
export const formatDate = (date, format = 'YYYY-MM-DD HH:mm:ss') => {
  return moment(date).format(format);
};

/**
 * Converts a date string to Unix timestamp (seconds)
 * @param {String} dateString - Date string (e.g., '2023-05-01')
 * @returns {Number} Unix timestamp in seconds
 */
export const toUnixTimestamp = (dateString) => {
  return moment(dateString).unix();
};

/**
 * Gets start of day timestamp for a given date
 * @param {String} dateString - Date string (e.g., '2023-05-01')
 * @returns {Number} Unix timestamp in seconds for start of day
 */
export const getStartOfDay = (dateString) => {
  return moment(dateString).startOf('day').unix();
};

/**
 * Gets end of day timestamp for a given date
 * @param {String} dateString - Date string (e.g., '2023-05-01')
 * @returns {Number} Unix timestamp in seconds for end of day
 */
export const getEndOfDay = (dateString) => {
  return moment(dateString).endOf('day').unix();
};

/**
 * Converts Unix timestamp to Date object
 * @param {Number} timestamp - Unix timestamp in seconds
 * @returns {Date} JavaScript Date object
 */
export const fromUnixTimestamp = (timestamp) => {
  return moment.unix(timestamp).toDate();
};

export default moment;
