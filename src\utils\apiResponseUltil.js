/**
 * Utility for standardizing API responses
 */

/**
 * Creates a standardized success response object
 * @param {string} message - Success message
 * @param {object|array} data - Response data (optional)
 * @returns {object} Formatted success response
 */
export const successResponse = (message, data = null) => {
  const response = {
    success: true,
    message,
  };

  if (data !== null) {
    response.data = data;
  }

  return response;
};

/**
 * Creates a standardized error response object
 * @param {string} message - Error message
 * @param {object|array} data - Additional error data (optional)
 * @param {string} stack - Error stack trace (only included in development)
 * @returns {object} Formatted error response
 */
export const errorResponse = (message, data = null, stack = null) => {
  const response = {
    success: false,
    message,
  };

  if (data !== null) {
    response.data = data;
  }

  if (process.env.NODE_ENV === 'development' && stack) {
    response.stack = stack;
  }

  return response;
};
