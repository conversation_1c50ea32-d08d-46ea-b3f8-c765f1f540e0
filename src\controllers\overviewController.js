import { errorResponse, successResponse } from '../utils/apiResponseUltil.js';
import * as statisticService from '../services/statisticService.js';
import { STATISTIC_TYPES, TIME_PERIODS } from '../config/constants.js';
import logger from '../config/logger.js';

export const getOverviewStats = async (req, res) => {
  try {
    const stats = await statisticService.getOverview();
    return res.status(200).json(stats);
  } catch (error) {
    logger.error('Error getting overview stats:', error);
    return res.status(500).json({ error: 'Failed to get overview statistics' });
  }
};

export const getOverviewByDate = async (req, res) => {
  try {
    const { type, time_period } = req.query;

    const typeNum = parseInt(type, 10);
    const timePeriodNum = parseInt(time_period, 10);

    if (!type || !time_period) {
      return res.status(400).json(errorResponse('Missing required parameters: type, time_period'));
    }

    const validTypes = Object.values(STATISTIC_TYPES);
    if (!validTypes.includes(typeNum)) {
      return res.status(400).json(errorResponse(`Invalid type. Must be ${validTypes.join(', ')}`));
    }

    const validTimePeriods = Object.values(TIME_PERIODS);
    if (!validTimePeriods.includes(timePeriodNum)) {
      return res
        .status(400)
        .json(errorResponse(`Invalid time_period. Must be ${validTimePeriods.join(', ')}`));
    }

    const result = await statisticService.getOverviewByDate({
      type: typeNum,
      time_period: timePeriodNum,
    });

    return res.status(200).json(result);
  } catch (error) {
    logger.error('Error getting overview by date:', error);
    return res.status(500).json(errorResponse('Failed to get overview statistics by date'));
  }
};

export const getDailyStatistics = async (req, res) => {
  try {
    const { start_date, end_date, type } = req.query;

    const typeNum = type ? parseInt(type, 10) : null;

    if (!start_date || !end_date) {
      return res
        .status(400)
        .json(errorResponse('Missing required parameters: start_date, end_date'));
    }

    if (
      typeNum !== null &&
      typeNum !== undefined &&
      ![STATISTIC_TYPES.REGISTERED_ACCOUNTS, STATISTIC_TYPES.REGISTERED_IPS].includes(typeNum)
    ) {
      return res
        .status(400)
        .json(
          errorResponse(
            `Invalid type. Must be ${STATISTIC_TYPES.REGISTERED_ACCOUNTS}, ${STATISTIC_TYPES.REGISTERED_IPS}, or null`
          )
        );
    }

    const result = await statisticService.getDailyStatistics({
      start_date,
      end_date,
      type: typeNum,
    });

    return res.status(200).json(result);
  } catch (error) {
    logger.error('Error getting daily statistics:', error);
    return res.status(500).json(errorResponse('Failed to get daily statistics'));
  }
};

export const getRevenueStatistics = async (req, res) => {
  try {
    const { start_date, end_date } = req.query;

    if (!start_date || !end_date) {
      return res
        .status(400)
        .json(errorResponse('Missing required parameters: start_date, end_date'));
    }

    const result = await statisticService.getRevenueStatistics({
      start_date,
      end_date,
    });

    return res.status(200).json(result);
  } catch (error) {
    logger.error('Error getting revenue statistics:', error);
    return res.status(500).json(errorResponse('Failed to get revenue statistics'));
  }
};

export const getTopRecharge = async (req, res) => {
  try {
    const { page = 1, limit = 10, time_period = TIME_PERIODS.ALL_TIME } = req.query;

    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);
    const timePeriodNum = parseInt(time_period, 10);

    if (pageNum < 1 || limitNum < 1) {
      return res.status(400).json(errorResponse('Page and limit must be positive integers'));
    }

    if (limitNum > 100) {
      return res.status(400).json(errorResponse('Limit cannot exceed 100'));
    }

    const validTimePeriods = Object.values(TIME_PERIODS);
    if (!validTimePeriods.includes(timePeriodNum)) {
      return res
        .status(400)
        .json(errorResponse(`Invalid time_period. Must be one of: ${validTimePeriods.join(', ')}`));
    }

    const result = await statisticService.getTopRecharge({
      page: pageNum,
      limit: limitNum,
      time_period: timePeriodNum,
    });

    return res.status(200).json(successResponse('Lấy dữ liệu top nạp thành công', result));
  } catch (error) {
    logger.error('Error getting top recharge:', error);
    return res.status(500).json(errorResponse('Failed to get top recharge statistics'));
  }
};

export const getTopPackages = async (req, res) => {
  try {
    const result = await statisticService.getTopPackages();

    return res.status(200).json(successResponse('Lấy dữ liệu top gói nạp thành công', result));
  } catch (error) {
    logger.error('Error getting top packages:', error);
    return res.status(500).json(errorResponse('Failed to get top packages statistics'));
  }
};

export const getRecentHistory = async (req, res) => {
  try {
    const { page = 1, limit = 10 } = req.query;

    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);

    if (pageNum < 1 || limitNum < 1) {
      return res.status(400).json(errorResponse('Page and limit must be positive integers'));
    }

    if (limitNum > 100) {
      return res.status(400).json(errorResponse('Limit cannot exceed 100'));
    }

    const result = await statisticService.getRecentHistory({
      page: pageNum,
      limit: limitNum,
    });

    return res.status(200).json(successResponse('Lấy dữ liệu lịch sử gần đây thành công', result));
  } catch (error) {
    logger.error('Error getting recent history:', error);
    return res.status(500).json(errorResponse('Failed to get recent history'));
  }
};
