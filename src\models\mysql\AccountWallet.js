import BaseModel from '../shared/BaseModel.js';
import { Fields, Queries } from '../shared/helpers.js';
import { Op } from 'sequelize';

export default class AccountWallet extends BaseModel {
  static get tableName() {
    return 'account_wallets';
  }

  static getFields(DataTypes) {
    return Object.assign({}, super.getFields(DataTypes), {
      accountId: Fields.accountId(DataTypes),
      accountName: Fields.accountName(DataTypes),

      balance: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: false,
        defaultValue: 0,
        comment: 'Số dư hiện tại',
        validate: { min: 0 },
      },

      coinBalance: Fields.coinAmount(DataTypes),

      status: {
        type: DataTypes.TINYINT,
        allowNull: false,
        defaultValue: 1,
        comment: 'Trạng thái ví: 0=Không hoạt động, 1=Hoạt động, 2=Tạm khóa',
        validate: { isIn: [[0, 1, 2]] },
      },

      metaData: Fields.metaData(DataTypes),
    });
  }

  static getOptions() {
    return Object.assign({}, super.getOptions(), {
      indexes: [
        { fields: ['account_id'], unique: true },
        { fields: ['status'] },
        { fields: ['balance'] },
        { fields: ['coin_balance'] },
      ],
    });
  }

  static getStatusEnum() {
    return {
      INACTIVE: 0,
      ACTIVE: 1,
      SUSPENDED: 2,
    };
  }

  // Tìm kiếm cơ bản
  static async findActive() {
    return await this.findAll({ where: { status: this.getStatusEnum().ACTIVE } });
  }

  static async findByAccount(accountId) {
    return await this.findOne(Queries.byAccount(accountId));
  }

  static async findWithBalance() {
    return await this.findAll({ where: { balance: { [Op.gt]: 0 } } });
  }

  // Thống kê cơ bản
  static async sumTotalBalance() {
    const result = await this.findOne({
      attributes: [[this.sequelize.fn('SUM', this.sequelize.col('balance')), 'total']],
    });
    return result ? parseFloat(result.getDataValue('total') || 0) : 0;
  }

  static async sumActiveBalance() {
    const result = await this.findOne({
      where: { status: this.getStatusEnum().ACTIVE },
      attributes: [[this.sequelize.fn('SUM', this.sequelize.col('balance')), 'total']],
    });
    return result ? parseFloat(result.getDataValue('total') || 0) : 0;
  }

  // Quản lý ví
  static async updateBalance(accountId, newBalance) {
    return await this.update({ balance: newBalance }, { where: { accountId } });
  }

  static async updateStatus(accountId, newStatus) {
    return await this.update({ status: newStatus }, { where: { accountId } });
  }

  static async createWallet(accountId, accountName, initialBalance = 0, initialCoinBalance = 0) {
    return await this.create({
      accountId,
      accountName,
      balance: initialBalance,
      coinBalance: initialCoinBalance,
      status: this.getStatusEnum().ACTIVE,
    });
  }
}
